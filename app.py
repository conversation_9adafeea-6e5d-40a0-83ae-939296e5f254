import tempfile
import os
import streamlit as st
import asyncio
from datetime import date, timedelta
from dotenv import load_dotenv
import hashlib
import base64
from pathlib import Path

load_dotenv()

from ocr_with_gpt4o import process_pdf, process_pdf_with_metadata
from gpt4o_vision import process_image_with_gpt4o, generate_content_with_gpt4o, generate_content_with_reasoning

from text_processing import process_text_in_segments
from case_builder import get_prompts
from user import login_user, insert_feedback, update_last_login
from casebuilder.app_integration import streamlit_adapter
from token_manager import TokenManager
from table_utils import display_text_with_tables

# Utility function to display text areas with proper accessibility
def accessible_text_area(text, height=400, key=None):
    """
    Display a text area with proper accessibility labels.

    Args:
        text (str): Text to display
        height (int): Height of the text area
        key (str, optional): Unique key for the component

    Returns:
        The text area component
    """
    return st.text_area("Analysis", text, height=height, label_visibility="collapsed", key=key)

# Initialize token manager
token_manager = TokenManager()




def LoggedIn_Clicked(userName, password):
    st.session_state['loggedIn'] = True
    result = login_user(userName,password)
    first_name = 'first_name'
    if result:
        st.session_state['loggedIn'] = True
        st.session_state['username'] = userName
        st.session_state[first_name] = result[0][3]
        # Update last login timestamp
        update_last_login(userName)
        st.success("Logged In as {}".format(userName))
    else:
        st.session_state['loggedIn'] = False
        st.session_state['username'] = None
        st.error("Invalid User Name or Password")


def LoggedOut_Clicked():
    keys_to_keep = ['show_document_warning', 'loggedIn', 'username', 'documents_uploaded']
    for key in list(st.session_state.keys()):
        if key not in keys_to_keep:
            del st.session_state[key]
    st.session_state['loggedIn'] = False
    st.session_state['username'] = None


def click_action_button(button_name):
    """Check if user has enough tokens and deduct them if so."""
    user_name = st.session_state['username']

    # Use the token manager to check and deduct tokens
    success = token_manager.check_and_deduct_tokens(user_name, button_name)

    # Update session state based on result
    st.session_state['cancelProcess'] = not success

    # Refresh the token count display
    if success:
        # Update the token count in the session state
        st.session_state['click_count'] = token_manager.get_user_tokens(user_name)
        print(f"Tokens remaining: {st.session_state['click_count']}")

    return success


def refresh_available_click_count(user_name):
    """Refresh the available token count in the session state."""
    print("refresh_available_click_count...")
    available_tokens = token_manager.get_user_tokens(user_name)
    st.session_state["click_count"] = available_tokens


def get_available_click_count(user_name):
    """Get the available token count for a user."""
    return token_manager.get_user_tokens(user_name)


def show_login_page():
    if st.session_state['loggedIn'] == False:

        userName = st.text_input (label="User", value="", placeholder="Enter User Name")
        password = st.text_input (label="Password", value="", placeholder="Enter Password", type="password")
        st.button ("Log In", on_click=LoggedIn_Clicked, args= (userName, password))

        button_style = """
            <style>
            .stButton>button {
                color: white;
                background-color: #00A8AC;
                padding: 10px 24px;
                border-radius: 8px;
                border: none;
                width: 150px;
                transition: background-color 0.3s, transform 0.3s;
            }
            .stButton>button:hover {
                background-color: #0099A8;
                transform: scale(1.05);
            }
            </style>
        """
        st.markdown(button_style, unsafe_allow_html=True)
        st.markdown("---")
        # Enlace de registro con estilo y efecto hover
        st.markdown(
            """
            <style>
              a.signup-link {
                color: #00A8AC;
                text-decoration: none;
                transition: color 0.3s, text-decoration 0.3s;
              }
              a.signup-link:hover {
                color: #0099A8;
                text-decoration: underline;
              }
            </style>
            <p style='color: #616161;'>Don't have an account?
              <a href='https://casebuilder.ai/free-trial' target='_blank' class='signup-link'>Sign up here</a>
            </p>
            """,
            unsafe_allow_html=True
        )
        st.markdown("---")


def show_sidebar():
    st.markdown("""
        <style>
            /* Aumenta ligeramente el ancho de la barra lateral para centrar siempre el logo */
            div[data-testid="stSidebar"] {
                width: 650px !important;    /* Ajusta este valor según sea necesario */
                min-width: 650px !important;
            }
        </style>
    """, unsafe_allow_html=True)

    # Logo en la barra lateral (siempre visible y centrado)
    display_sidebar_logo(width=370)
    username = st.session_state['username']
    first_name = st.session_state['first_name']
    st.sidebar.markdown(f"Welcome, {first_name}.")
    st.sidebar.button("Log Out", key="logout", on_click=LoggedOut_Clicked)

    if "click_count" not in st.session_state:
        st.session_state.click_count = get_available_click_count(username)

    if "click_count" in st.session_state:
        st.sidebar.write(f"Tokens Remaining: {st.session_state['click_count']}")

    st.sidebar.markdown(
        f"""
        <div style="padding-top:1px">
            <a href="https://casebuilder.ai/pricing" target="_blank" style="text-decoration: none;">
                <button style="border: none; padding: 10px 20px; font-size: 16px; cursor: pointer; border-radius: 5px;
                color: white;
                background-color: #00A8AC;
                width: 100%;
                transition: background-color 0.3s, transform 0.3s;">
                    Buy Tokens ✪
                </button>
            </a>
        </div>

        """,
        unsafe_allow_html=True
    )
    st.sidebar.markdown("---")

    with st.sidebar.expander("#### ⚙️ Case Settings", expanded=False):
        states = ["Select a State"] + sorted([
            'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia',
            'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts',
            'Michigan', 'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico',
            'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina',
            'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
        ])
        selected_state = st.selectbox("Select State", states, help="Select the state where the incident occurred.")
        if selected_state != "Select a State":
            st.session_state['selected_state'] = selected_state

        case_options = ['Traffic Collision', 'Premises Liability', 'Slip & Fall', 'Dog Bite']
        selected_case = st.selectbox("Select Case Type", case_options, index=0, help="Select the type of case you are working on.")
        st.session_state['case_type'] = selected_case

        if selected_case == 'Traffic Collision':
            liability_options = ['Bodily Injury Liability', 'Uninsured Motorist', 'Under-Insured Motorist']
        else:
            liability_options = ['Bodily Injury Liability']
        liability_type = st.radio(
            "### Choose Liability Type",
            liability_options,
            help="Select the type of liability involved in the case."
        )
        st.session_state['liability_type'] = liability_type

        if liability_type == 'Bodily Injury Liability':
            liability_accepted = st.radio("Liability Accepted", ['Yes', 'No'], index=1, help="Has the other party accepted liability for the accident?")
            if liability_accepted == 'Yes':
                liability_percentage = st.slider(
                    "Percentage of Liability Accepted", 0, 100, 100, help="Enter the percentage of liability accepted."
                )
                st.session_state['liability_accepted'] = True
                st.session_state['liability_percentage'] = liability_percentage
            else:
                st.session_state['liability_accepted'] = False
                st.session_state['liability_percentage'] = 0

            demand_amount_selection = st.radio(
                "Set the demand amount",
                ('Policy Limit', 'Enter Amount'),
                help="Choose whether to use the policy limit or enter a specific demand amount."
            )
            if demand_amount_selection == 'Enter Amount':
                demand_amount_input = st.text_input("Demand Amount", value="$", placeholder= "Enter Demand Amount")
                if demand_amount_input.startswith("$"):
                    demand_amount_input = demand_amount_input[1:]  # Remove $ if present
                if "." in demand_amount_input:
                    st.error("Please avoid using decimal points.")
                    st.stop()
                elif demand_amount_input:
                    try:
                        demand_amount = float(demand_amount_input.replace(",", ""))
                        st.session_state['demand_amount'] = demand_amount
                    except ValueError:
                        st.error("Please enter a valid dollar amount.")
                        st.stop()
                else:
                    st.info("Please enter the demand amount.")
                    st.stop()
            else:
                st.session_state['demand_amount'] = 'Policy Limit'

        lost_income_input = st.text_input("Loss of Wages ($)", value="", placeholder= "Enter Loss of Wages", help="Enter the amount of lost wages due to the incident.")
        if lost_income_input:
            try:
                lost_income = float(lost_income_input.replace(",", "").replace("$", ""))
                st.session_state['lost_income'] = lost_income
            except ValueError:
                st.error("Please enter a valid dollar amount.")
                st.stop()
        else:
            st.session_state['lost_income'] = 0

        default_date = date.today() + timedelta(days=30)
        due_date = st.date_input("Due Date", value=default_date, help="Select the due date for the demand letter.")
        st.session_state['due_date'] = due_date

        streamlit_adapter.setup_police_report_preferences()
        streamlit_adapter.setup_medical_analysis_preferences()
        streamlit_adapter.setup_demand_letter_preferences()

        if st.button("Supplemental Case Information"):
            if selected_state != "Select a State":
                # Check if chat dialog is already open
                if st.session_state.get("show_chat_dialog", False):
                    st.warning("⚠️ Streamlit only allows one dialog at a time. Please use the '✕ Close Assistant' button in the CaseBuilder Assistant dialog, or refresh the page before opening this dialog.")
                else:
                    st.session_state['show_dialog'] = True
            else:
                st.error("Please select a state before entering case information.")

            if 'show_dialog' in st.session_state and st.session_state['show_dialog']:
                try:
                    show_input_dialog()
                except Exception as e:
                    # Handle the StreamlitAPIException gracefully
                    if "Only one dialog is allowed to be opened at the same time" in str(e):
                        st.warning("⚠️ Streamlit only allows one dialog at a time. Please close the other dialog using its close button, or refresh the page to continue.")
                        # Reset the dialog state to avoid getting stuck
                        st.session_state['show_dialog'] = False
                    else:
                        # Re-raise other exceptions
                        raise e


    st.sidebar.markdown("---")

    # Instructions expander in sidebar
    with st.sidebar.expander("📋 Quick Start Guide", expanded=False):
        st.markdown("""
        ### How to Use CaseBuilder in 3 simple steps:
        1. **Set your preferences**
           - Use the sidebar to adjust case settings, summary detail level, and style options.

        2. **Upload your case documents**
           - Drag and drop incident-related photos, medical records and/or police reports directly into the platform.

        3. **Click to generate**
           - Generate analysis, summaries or demand letters with a single click.
        """)

        # Add tips in a warning component for better visibility
        st.warning("""
        **Tips for Best Results:**\n
        • Configure all settings first before uploading files\n
        • Changing settings after upload will trigger a reload\n
        • Upload files after setting preferences for faster processing
        """)

    # FAQ section in sidebar
    with st.sidebar.expander("❓ Frequently Asked Questions", expanded=False):
        # Create tabs for different FAQ categories
        faq_tabs = st.tabs(["General", "Documents", "Analysis", "Demand Letters", "Troubleshooting"])

        # General FAQs
        with faq_tabs[0]:
            st.markdown("""
            ### General Questions

            #### What is CaseBuilder?
            CaseBuilder is an AI-powered platform designed to help legal professionals analyze personal injury cases and generate demand letters efficiently.

            #### How do I get started?
            1. Set your preferences in the sidebar
            2. Upload your case documents
            3. Use the analysis buttons to generate insights
            4. Generate a demand letter when ready

            #### How many tokens do I need for each action?
            Different actions require different numbers of tokens:
            - Medical Analysis: 1 token
            - Police Report Analysis: 1 token
            - Demand Letter Generation: 5 tokens
            - Image Analysis: 1 token per image
            """)

        # Documents FAQs
        with faq_tabs[1]:
            st.markdown("""
            ### Document Questions

            #### What document types can I upload?
            CaseBuilder supports:
            - Medical records (PDF)
            - Police reports (PDF)
            - Images of injuries or accident scenes (JPG, PNG)
            - Other case-related documents (PDF)

            #### Is there a file size limit?
            Yes, the maximum file size is 200MB per file. For optimal performance, we recommend keeping files under 50MB.
            """)

        # Analysis FAQs
        with faq_tabs[2]:
            st.markdown("""
            ### Analysis Questions

            #### What types of analysis can I perform?
            Depending on the documents you upload, you can:
            - Generate medical record summaries
            - Analyze police reports for liability
            - Identify medical expenses
            - Project future treatments
            - Analyze images for injuries or property damage
            """)

        # Demand Letters FAQs
        with faq_tabs[3]:
            st.markdown("""
            ### Demand Letter Questions

            #### What information is included in the demand letter?
            The demand letter includes:
            - Case facts and liability analysis
            - Medical treatment summary
            - Medical expenses (current and future)
            - Impact on lifestyle
            - General damages assessment
            - Visual evidence analysis (if images were uploaded)
            - Demand amount
            """)

        # Troubleshooting FAQs
        with faq_tabs[4]:
            st.markdown("""
            ### Troubleshooting

            #### Why is document processing taking so long?
            Processing time depends on document size and complexity. Large medical records or police reports may take several minutes to process.

            #### Why am I not seeing certain analysis buttons?
            The available buttons depend on the types of documents you've uploaded. For example, medical analysis buttons only appear when medical records are detected.
            """)

    # Add the Ask CaseBuilder button below the FAQ expander
    if st.sidebar.button("🤖 Ask CaseBuilder", key="sidebar_chat_button"):
        # Check if the other dialog is already open
        if st.session_state.get('show_dialog', False):
            st.sidebar.warning("⚠️ Streamlit only allows one dialog at a time. Please use the '✕ Close Form' button in the Case Information dialog, or refresh the page before opening this dialog.")
        else:
            st.session_state["show_chat_dialog"] = True
            st.rerun()

    st.sidebar.markdown("---")
    # Feedback dialog trigger - TEMPORARILY DISABLED TO AVOID STREAMLIT DIALOG CONFLICT
    if 'show_feedback_dialog' not in st.session_state:
        st.session_state['show_feedback_dialog'] = False
    # Commented out to avoid conflict with CaseBuilder Assistant dialog
    # if st.sidebar.button("We'd Love Your Feedback", key="open_feedback"):
    #     st.session_state['show_feedback_dialog'] = True
    # if st.session_state.get('show_feedback_dialog', False):
    #     # Display feedback dialog form
    #     show_feedback_dialog()

    # Show a regular link instead of a dialog
    # st.sidebar.markdown("[We'd Love Your Feedback](mailto:<EMAIL>?subject=CaseBuilder%20Feedback)")

    # Enlace de contacto con estilo y hover
    st.sidebar.markdown(
        """
        <style>
          a.contact-link {
            color: #00A8AC;
            text-decoration: none;
            transition: color 0.3s, text-decoration 0.3s;
          }
          a.contact-link:hover {
            color: #0099A8;
            text-decoration: underline;
          }
        </style>
        <p style='color: #616161;'>Need help?
          <a href='https://casebuilder.ai/contact' target='_blank' class='contact-link'>Contact Support</a>
        </p>
        """,
        unsafe_allow_html=True
    )
    st.sidebar.markdown(
         """
        <style>
          a.feedback-link {
            color: #00A8AC;
            text-decoration: none;
            transition: color 0.3s, text-decoration 0.3s;
          }
          a.feedback-link:hover {
            color: #0099A8;
            text-decoration: underline;
          }
        </style>
        <p style='color: #616161;'>Have feedback?
          <a href='mailto:<EMAIL>?subject=CaseBuilder%20Feedback' class='feedback-link'>We'd Love to Hear From You!</a>
        </p>
        """,
        unsafe_allow_html=True
    )

def apply_button_style():
    button_style = """
        <style>
        div.stButton > button {
            background-color: #00A8AC;  /* Color de fondo del botón */
            color: #ffffff;            /* Color del texto */
            width: 100%;               /* Ancho del botón */
            margin: 5px;               /* Margen alrededor del botón */
            padding: 10px 24px;        /* Espaciado interno del botón */
            border-radius: 8px;        /* Redondeo de las esquinas */
            border: none;              /* Sin borde */
            transition: background-color 0.3s, transform 0.3s;  /* Transición para hover */
        }
        div.stButton > button:hover {
            background-color: #0099A8; /* Color de fondo al pasar el mouse */
            transform: scale(1.05);    /* Efecto de escala al pasar el mouse */
        }
        </style>
    """
    st.markdown(button_style, unsafe_allow_html=True)


async def show_unified_document_section():
    """Show a unified document uploader section that handles all document types."""
    # Use the streamlit_adapter to show the unified uploader
    await streamlit_adapter.show_unified_uploader(
        process_pdf_func=process_pdf,
        process_pdf_with_metadata_func=process_pdf_with_metadata,
        process_image_func=process_image_with_gpt4o
    )

    # Handle action buttons if an action is selected
    if 'action' in st.session_state:
        action = st.session_state['action']
        action_title = st.session_state.get('action_title', '')

        # Check if user has enough tokens for the action
        user_name = st.session_state['username']
        if not token_manager.check_and_deduct_tokens(user_name, action):
            st.error("You have reached your maximum number of tokens this month, please buy more or upgrade to a new plan.")
            return

        # Update token count in session state
        st.session_state['click_count'] = token_manager.get_user_tokens(user_name)

        # Display the action title
        st.markdown(f"### {action_title}")

        # Process the selected action
        with st.spinner("Processing..."):
                if action == 'facts_liability':
                    # Facts and liability analysis
                    result = await streamlit_adapter.extract_facts_and_liability()
                    if result and result.strip():
                        accessible_text_area(result, height=400, key="facts_liability_display")
                        st.session_state['facts_liability_result'] = result
                        st.download_button(
                            "Download Analysis",
                            result,
                            "facts_liability_analysis.txt",
                            "text/plain",
                            key="download_facts_liability"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")

                elif action == 'medical_analysis':
                    # Medical analysis
                    result = await streamlit_adapter.comprehensive_medical_analysis()
                    if result and result.strip():
                        # Check if the result contains ICD codes
                        has_icd_codes = "ICD DIAGNOSIS CODES TABLE" in result or "PROJECTED ICD DIAGNOSIS CODES TABLE" in result or "ICD-10 Code" in result

                        # Get the selected style from preferences
                        prefs = st.session_state.get('medical_analysis_preferences', {'style': 'narrative'})
                        style = prefs.get('style', 'narrative')

                        # If it has ICD codes, use display_text_with_tables regardless of style
                        if has_icd_codes:
                            display_text_with_tables(result, style=style)
                        else:
                            accessible_text_area(result, height=400, key="medical_analysis_display")

                        st.session_state['medical_analysis_result'] = result
                        st.download_button(
                            "Download Analysis",
                            result,
                            "medical_analysis.txt",
                            "text/plain",
                            key="download_medical_analysis"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")

                elif action == 'medical_expenses':
                    # Medical expenses analysis
                    result = await streamlit_adapter.identify_medical_expenses()
                    if result and result.strip():
                        # Get the selected style from preferences
                        prefs = st.session_state.get('medical_analysis_preferences', {'style': 'tabular'})
                        style = prefs.get('style', 'tabular')

                        # Check if the result contains ICD codes
                        has_icd_codes = "ICD DIAGNOSIS CODES TABLE" in result or "PROJECTED ICD DIAGNOSIS CODES TABLE" in result or "ICD-10 Code" in result

                        # If it has ICD codes or tabular style, use display_text_with_tables
                        if style.lower() == 'tabular' or has_icd_codes:
                            display_text_with_tables(result, style=style)
                        else:
                            accessible_text_area(result, height=400, key="medical_expenses_display")

                        st.session_state['medical_expenses_result'] = result
                        st.download_button(
                            "Download Analysis",
                            result,
                            "medical_expenses.txt",
                            "text/plain",
                            key="download_medical_expenses"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")

                elif action == 'analyze_injuries':
                    # Injury analysis from images
                    if 'uploaded_images' in st.session_state and st.session_state.uploaded_images:
                        # Get context from documents and additional notes
                        document_context = st.session_state.get('combined_text_buffer', '')
                        additional_notes = st.session_state.get('additional_notes', '')
                        injuries_info = st.session_state.get('injuries', '')

                        # Combine all context information
                        combined_image_text = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}\n\nInjury Details: {injuries_info}"
                        client_name = st.session_state.get('client_name', 'the client')

                        full_instruction = f"""
                        Based on the context provided: {combined_image_text}, please describe the injuries shown in the image of {client_name}.
                        Include the following points:
                        1. Detailed description of visible injuries on {client_name}.
                        2. Relation of these injuries to the context provided.
                        3. How these injuries support claims for damages and liability due to the negligent actions of the at-fault driver.
                        4. A thorough analysis to be used in a legal narrative, highlighting the impact and severity of the injuries.
                        """

                        image_text = await process_image_with_gpt4o(st.session_state.uploaded_images[0], full_instruction)
                        accessible_text_area(image_text, height=400, key="image_analysis_display")
                        st.session_state['image_analysis_result'] = image_text

                        st.download_button(
                            "Download Analysis",
                            image_text,
                            "injury_analysis.txt",
                            "text/plain",
                            key="download_injury_analysis"
                        )
                    else:
                        st.error("No images found for analysis. Please upload images first.")

                elif action == 'accident_scene':
                    # Accident scene analysis from images
                    if 'uploaded_images' in st.session_state and st.session_state.uploaded_images:
                        # Get context from documents and additional notes
                        document_context = st.session_state.get('combined_text_buffer', '')
                        additional_notes = st.session_state.get('additional_notes', '')
                        accident_info = st.session_state.get('facts_liability_result', '')

                        # Combine all context information
                        combined_image_text = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}\n\nAccident Information: {accident_info}"
                        client_name = st.session_state.get('client_name', 'the client')

                        individual_results = []
                        for idx, image in enumerate(st.session_state.uploaded_images):
                            image_instruction = f"""
                            Analyze image {idx+1} as part of a sequence capturing the accident scene involving {client_name}.
                            Extract relevant details such as vehicle positions, damages, environmental context, and any clues regarding the sequence of events.
                            """
                            result = await process_image_with_gpt4o(image, image_instruction)
                            individual_results.append(f"Image {idx+1} Analysis:\n{result}")

                        combined_input = "\n\n".join(individual_results)
                        full_instruction = f"""
                            Based on the context provided: {combined_image_text} and the following detailed analyses of each image in the accident sequence,
                            reconstruct a comprehensive and legally sound narrative of the accident scene involving {client_name}.
                            Your analysis should include:

                            1. A precise, step-by-step description of each phase of the accident as captured in the images.
                            2. An explanation of how the visual evidence (such as vehicle positions, damage details, environmental conditions, and other contextual clues) establishes a clear chronological sequence of events.
                            3. A correlation between the visual details and the additional context provided, emphasizing any evidence of negligence or contributory factors.
                            4. An assessment of the impact on {client_name}, including potential injuries and property damage.
                            5. A detailed narrative formatted for legal use in a personal injury lawsuit, clearly linking the visual evidence to claims for damages and liability.

                            Please ensure that the final output is thorough, precise, and suitable for inclusion in legal documentation.
                            """
                        scene_analysis = await generate_content_with_reasoning(combined_input, full_instruction)
                        accessible_text_area(scene_analysis, height=400, key="accident_scene_display")
                        st.session_state['accident_scene_analysis'] = scene_analysis

                        st.download_button(
                            "Download Analysis",
                            scene_analysis,
                            "accident_scene_analysis.txt",
                            "text/plain",
                            key="download_accident_scene"
                        )
                    else:
                        st.error("No images found for analysis. Please upload images first.")

                elif action == 'police_report_summary':
                    # Police report summary
                    result = await streamlit_adapter.police_report_summary()
                    if result and result.strip():
                        accessible_text_area(result, height=400, key="police_report_summary_display")
                        st.session_state['police_report_summary_result'] = result
                        st.download_button(
                            "Download Summary",
                            result,
                            "police_report_summary.txt",
                            "text/plain",
                            key="download_police_summary"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")

                elif action == 'in_depth_liability':
                    # In-depth liability analysis
                    result = await streamlit_adapter.in_depth_liability_analysis()
                    if result and result.strip():
                        accessible_text_area(result, height=400, key="in_depth_liability_display")
                        st.session_state['in_depth_liability_result'] = result
                        st.download_button(
                            "Download Analysis",
                            result,
                            "in_depth_liability_analysis.txt",
                            "text/plain",
                            key="download_in_depth_liability"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")

                elif action == 'property_damage':
                    # Property damage analysis from images
                    if 'uploaded_images' in st.session_state and st.session_state.uploaded_images:
                        # Get context from documents and additional notes
                        document_context = st.session_state.get('combined_text_buffer', '')
                        additional_notes = st.session_state.get('additional_notes', '')
                        accident_info = st.session_state.get('facts_liability_result', '')

                        # Combine all context information
                        combined_image_text = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}\n\nAccident Information: {accident_info}"
                        client_name = st.session_state.get('client_name', 'the client')

                        full_instruction = f"""
                        Based on the context provided: {combined_image_text}, please analyze the property damage shown in the image(s).
                        Include the following points:
                        1. Detailed description of visible damage to vehicles or other property.
                        2. Assessment of the severity and extent of the damage.
                        3. Potential repair costs and impact on property value.
                        4. How the damage pattern supports claims about the accident mechanics.
                        5. Any safety implications of the damage.
                        6. How this evidence supports the liability claim against the at-fault party.

                        Provide a thorough analysis suitable for inclusion in legal documentation.
                        """

                        if len(st.session_state.uploaded_images) > 1:
                            # Multiple images - analyze each and then combine
                            individual_results = []
                            for idx, image in enumerate(st.session_state.uploaded_images):
                                image_instruction = f"Analyze the property damage visible in image {idx+1}."
                                result = await process_image_with_gpt4o(image, image_instruction)
                                individual_results.append(f"Image {idx+1} Analysis:\n{result}")

                            combined_input = "\n\n".join(individual_results)
                            damage_analysis = await generate_content_with_reasoning(combined_input, full_instruction)
                        else:
                            # Single image analysis
                            damage_analysis = await process_image_with_gpt4o(st.session_state.uploaded_images[0], full_instruction)

                        accessible_text_area(damage_analysis, height=400, key="property_damage_display")
                        st.session_state['property_damage_analysis'] = damage_analysis
                        st.download_button(
                            "Download Analysis",
                            damage_analysis,
                            "property_damage_analysis.txt",
                            "text/plain",
                            key="download_property_damage"
                        )
                    else:
                        st.error("No images found for analysis. Please upload images first.")

                elif action == 'future_treatment':
                    # Future treatment analysis
                    result = await streamlit_adapter.identify_future_treatments()
                    if result and result.strip():
                        # Get the selected style from preferences
                        prefs = st.session_state.get('medical_analysis_preferences', {'style': 'tabular'})
                        style = prefs.get('style', 'tabular')

                        # Check if the result contains ICD codes
                        has_icd_codes = "ICD DIAGNOSIS CODES TABLE" in result or "PROJECTED ICD DIAGNOSIS CODES TABLE" in result or "ICD-10 Code" in result

                        # If it has ICD codes or tabular style, use display_text_with_tables
                        if style.lower() == 'tabular' or has_icd_codes:
                            display_text_with_tables(result, style=style)
                        else:
                            accessible_text_area(result, height=400, key="future_treatment_display")

                        st.session_state['future_treatments_result'] = result
                        st.download_button(
                            "Download Analysis",
                            result,
                            "future_treatments.txt",
                            "text/plain",
                            key="download_future_treatments"
                        )
                    else:
                        st.error("No results were generated. Please try again or check your documents.")



                elif action == 'case_law':
                    # Case law references
                    combined_text = st.session_state.get('combined_text_buffer', '')
                    case_text = await process_text_in_segments(
                        model_alias="gpt4o",
                        text=combined_text,
                        prompt=get_prompts('case_law')
                    )
                    accessible_text_area(case_text, height=400, key="case_law_display")
                    st.session_state['case_law_text'] = case_text
                    st.download_button(
                        "Download Case Law References",
                        case_text,
                        "case_law_references.txt",
                        "text/plain",
                        key="download_case_law"
                    )

                elif action == 'legal_framework':
                    # Legal framework
                    combined_text = st.session_state.get('combined_text_buffer', '')
                    legal_text = await process_text_in_segments(
                        model_alias="gpt4o",
                        text=combined_text,
                        prompt=get_prompts('legal_framework')
                    )
                    accessible_text_area(legal_text, height=400, key="legal_framework_display")
                    st.session_state['legal_framework'] = legal_text
                    st.download_button(
                        "Download Legal Framework",
                        legal_text,
                        "legal_framework.txt",
                        "text/plain",
                        key="download_legal_framework"
                    )

                elif action == 'demand_letter':
                    # Generate demand letter
                    st.session_state['generate_demand'] = True

                    # Initialize buffers if they don't exist
                    if 'prelit_text_buffer' not in st.session_state:
                        st.session_state['prelit_text_buffer'] = st.session_state.get('combined_text_buffer', '')

                    if 'tcr_text_buffer' not in st.session_state:
                        st.session_state['tcr_text_buffer'] = st.session_state.get('combined_text_buffer', '')

                    if 'text_buffer' not in st.session_state:
                        st.session_state['text_buffer'] = st.session_state.get('combined_text_buffer', '')

                    # Process all steps for demand letter generation
                    def update_step(step_key, completed=False):
                        if completed:
                            st.session_state[step_key + '_done'] = True

                    # Create a placeholder for the progress
                    progress_placeholder = st.empty()

                    # Show progress
                    progress_bar = progress_placeholder.progress(0)

                    # Step 1: Facts and Liability
                    if 'step_1_done' not in st.session_state:
                        result = await streamlit_adapter.extract_facts_and_liability()
                        # Result is already stored in facts_liability_result in the adapter
                        update_step('step_1', completed=True)
                        progress_bar.progress(16)

                    # Step 2: Medical Analysis
                    if 'step_2_done' not in st.session_state:
                        result = await streamlit_adapter.comprehensive_medical_analysis()
                        # Result is already stored in medical_analysis_result in the adapter
                        update_step('step_2', completed=True)
                        progress_bar.progress(32)

                    # Step 2b: Medical Expenses
                    if 'step_2b_done' not in st.session_state:
                        # Force tabular style for demand letter generation
                        if 'medical_analysis_preferences' not in st.session_state:
                            st.session_state['medical_analysis_preferences'] = {}
                        st.session_state['medical_analysis_preferences']['style'] = 'tabular'

                        result = await streamlit_adapter.identify_medical_expenses()
                        # Result is already stored in medical_expenses_result in the adapter

                        # Use spinner for better UX
                        with st.spinner("💰 Medical expenses analysis completed in tabular format"):
                            pass

                        update_step('step_2b', completed=True)
                        progress_bar.progress(48)

                    # Step 3: Future Treatments
                    if 'step_3_done' not in st.session_state:
                        # Force tabular style for demand letter generation
                        if 'medical_analysis_preferences' not in st.session_state:
                            st.session_state['medical_analysis_preferences'] = {}
                        st.session_state['medical_analysis_preferences']['style'] = 'tabular'

                        result = await streamlit_adapter.identify_future_treatments()
                        # Result is already stored in future_treatments_result in the adapter

                        # Use spinner for better UX
                        with st.spinner("🔮 Future treatments analysis completed in tabular format"):
                            pass

                        update_step('step_3', completed=True)
                        progress_bar.progress(64)

                    # Step 4: Impact on Lifestyle
                    if 'step_4_done' not in st.session_state:
                        result = await streamlit_adapter.impact_on_lifestyle()
                        # Explicitly store the result in the session state
                        st.session_state['lifestyle_impact_result'] = result
                        update_step('step_4', completed=True)
                        progress_bar.progress(80)

                    # Step 5: General Damages
                    if 'step_5_done' not in st.session_state:
                        result = await streamlit_adapter.identify_general_damages()
                        # Explicitly store the result in the session state
                        st.session_state['general_damages_result'] = result
                        update_step('step_5', completed=True)
                        progress_bar.progress(80)

                    # Step 5b: Image Analysis (if images are available)
                    has_images = 'image' in st.session_state.get('document_types', set())
                    if has_images and 'step_5b_done' not in st.session_state:
                        # Check if we already have image analysis results
                        if 'image_analysis_result' not in st.session_state and 'uploaded_images' in st.session_state and len(st.session_state.uploaded_images) > 0:
                            # Get context from documents and additional notes
                            document_context = st.session_state.get('combined_text_buffer', '')
                            additional_notes = st.session_state.get('additional_notes', '')
                            injuries_info = st.session_state.get('injuries', '')

                            # Combine all context information
                            combined_image_text = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}\n\nInjury Details: {injuries_info}"
                            client_name = st.session_state.get('client_name', 'the client')

                            full_instruction = f"""
                            Based on the context provided: {combined_image_text}, please describe the injuries shown in the image of {client_name}.
                            Include the following points:
                            1. Detailed description of visible injuries on {client_name}.
                            2. Relation of these injuries to the context provided.
                            3. How these injuries support claims for damages and liability due to the negligent actions of the at-fault driver.
                            4. A thorough analysis to be used in a legal narrative, highlighting the impact and severity of the injuries.
                            """

                            with st.spinner("📸 Analyzing uploaded images for injury evidence..."):
                                image_text = await process_image_with_gpt4o(st.session_state.uploaded_images[0], full_instruction)
                            st.session_state['image_analysis_result'] = image_text

                        # Process accident scene if we have multiple images
                        if 'accident_scene_analysis' not in st.session_state and 'uploaded_images' in st.session_state and len(st.session_state.uploaded_images) > 1:
                            with st.spinner("📸 Analyzing accident scene from uploaded images..."):
                                # Get context from documents and additional notes
                                document_context = st.session_state.get('combined_text_buffer', '')
                                additional_notes = st.session_state.get('additional_notes', '')
                                accident_info = st.session_state.get('facts_liability_result', '')

                                # Combine all context information
                                combined_image_text = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}\n\nAccident Information: {accident_info}"
                                client_name = st.session_state.get('client_name', 'the client')

                                individual_results = []
                                for idx, image in enumerate(st.session_state.uploaded_images):
                                    image_instruction = f"""
                                    Analyze image {idx+1} as part of a sequence capturing the accident scene involving {client_name}.
                                    Extract relevant details such as vehicle positions, damages, environmental context, and any clues regarding the sequence of events.
                                    """
                                    result = await process_image_with_gpt4o(image, image_instruction)
                                    individual_results.append(f"Image {idx+1} Analysis:\n{result}")

                                combined_input = "\n\n".join(individual_results)
                                full_instruction = f"""
                                    Based on the context provided: {combined_image_text} and the following detailed analyses of each image in the accident sequence,
                                    reconstruct a comprehensive and legally sound narrative of the accident scene involving {client_name}.
                                    Your analysis should include:

                                    1. A precise, step-by-step description of each phase of the accident as captured in the images.
                                    2. An explanation of how the visual evidence (such as vehicle positions, damage details, environmental conditions, and other contextual clues) establishes a clear chronological sequence of events.
                                    3. A correlation between the visual details and the additional context provided, emphasizing any evidence of negligence or contributory factors.
                                    4. An assessment of the impact on {client_name}, including potential injuries and property damage.
                                    5. A detailed narrative formatted for legal use in a personal injury lawsuit, clearly linking the visual evidence to claims for damages and liability.

                                    Please ensure that the final output is thorough, precise, and suitable for inclusion in legal documentation.
                                    """
                                scene_analysis = await generate_content_with_reasoning(combined_input, full_instruction)
                                st.session_state['accident_scene_analysis'] = scene_analysis

                        update_step('step_5b', completed=True)
                        progress_bar.progress(90)

                    # Step 6: Demand Letter
                    if 'step_6_done' not in st.session_state:
                        # Generate the demand letter (preferences already set in sidebar)
                        result = await streamlit_adapter.create_demand_letter()
                        st.session_state['final_demand_letter'] = result
                        update_step('step_6', completed=True)
                        progress_bar.progress(100)

                        # Remove progress bar
                        progress_placeholder.empty()
                        st.session_state['demand_letter_generated'] = True

                    # Display the final demand letter
                    if 'final_demand_letter' in st.session_state and st.session_state['final_demand_letter'].strip():
                        st.markdown("### Final Demand Letter")
                        accessible_text_area(st.session_state['final_demand_letter'], height=600, key="final_demand_letter_display")

                        # Add download button
                        st.download_button(
                            "Download Demand Letter",
                            st.session_state['final_demand_letter'],
                            "demand_letter.txt",
                            "text/plain",
                            key="download_demand_letter"
                        )

                        # Add expanders for intermediate analyses
                        st.markdown("### Analysis Components")
                        st.markdown("Expand the sections below to view the detailed analyses used to generate the demand letter.")

                        # Facts and Liability Analysis
                        if 'facts_liability_result' in st.session_state and st.session_state['facts_liability_result'].strip():
                            with st.expander("Facts and Liability Analysis", expanded=False):
                                accessible_text_area(st.session_state['facts_liability_result'], height=300, key="facts_liability_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['facts_liability_result'],
                                    "facts_liability_analysis.txt",
                                    "text/plain",
                                    key="download_facts_liability_expander"
                                )

                        # Medical Analysis
                        if 'medical_analysis_result' in st.session_state and st.session_state['medical_analysis_result'].strip():
                            with st.expander("Medical Analysis", expanded=False):
                                accessible_text_area(st.session_state['medical_analysis_result'], height=300, key="medical_analysis_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['medical_analysis_result'],
                                    "medical_analysis.txt",
                                    "text/plain",
                                    key="download_medical_analysis_expander"
                                )

                        # Medical Expenses
                        if 'medical_expenses_result' in st.session_state and st.session_state['medical_expenses_result'].strip():
                            with st.expander("Medical Expenses", expanded=False):
                                # Always display in tabular format for demand letter
                                display_text_with_tables(st.session_state['medical_expenses_result'], style='tabular')

                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['medical_expenses_result'],
                                    "medical_expenses.txt",
                                    "text/plain",
                                    key="download_medical_expenses_expander"
                                )

                        # Future Medical Expenses
                        if 'future_treatments_result' in st.session_state and st.session_state['future_treatments_result'].strip():
                            with st.expander("Future Medical Expenses", expanded=False):
                                # Always display in tabular format for demand letter
                                display_text_with_tables(st.session_state['future_treatments_result'], style='tabular')

                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['future_treatments_result'],
                                    "future_treatments.txt",
                                    "text/plain",
                                    key="download_future_treatments_expander"
                                )

                        # Lifestyle Impact
                        if 'lifestyle_impact_result' in st.session_state and st.session_state['lifestyle_impact_result'].strip():
                            with st.expander("Impact on Lifestyle", expanded=False):
                                accessible_text_area(st.session_state['lifestyle_impact_result'], height=300, key="lifestyle_impact_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['lifestyle_impact_result'],
                                    "lifestyle_impact.txt",
                                    "text/plain",
                                    key="download_lifestyle_impact_expander"
                                )

                        # General Damages
                        if 'general_damages_result' in st.session_state and st.session_state['general_damages_result'].strip():
                            with st.expander("General Damages", expanded=False):
                                accessible_text_area(st.session_state['general_damages_result'], height=300, key="general_damages_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['general_damages_result'],
                                    "general_damages.txt",
                                    "text/plain",
                                    key="download_general_damages_expander"
                                )

                        # Image Analysis (if available)
                        if 'image_analysis_result' in st.session_state and st.session_state['image_analysis_result'].strip():
                            with st.expander("Image Analysis", expanded=False):
                                accessible_text_area(st.session_state['image_analysis_result'], height=300, key="image_analysis_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['image_analysis_result'],
                                    "image_analysis.txt",
                                    "text/plain",
                                    key="download_image_analysis_expander"
                                )

                        # Accident Scene Analysis (if available)
                        if 'accident_scene_analysis' in st.session_state and st.session_state['accident_scene_analysis'].strip():
                            with st.expander("Accident Scene Analysis", expanded=False):
                                accessible_text_area(st.session_state['accident_scene_analysis'], height=300, key="accident_scene_expander")
                                st.download_button(
                                    "Download Analysis",
                                    st.session_state['accident_scene_analysis'],
                                    "accident_scene_analysis.txt",
                                    "text/plain",
                                    key="download_accident_scene_expander"
                                )
                    elif 'step_6_done' in st.session_state:
                        st.error("No demand letter was generated. Please try again or check your documents.")

        # Clear the action after processing
        if 'action' in st.session_state and action != 'demand_letter':
            del st.session_state['action']

    st.markdown("---")


def make_hashes(password):
    return hashlib.sha256(str.encode(password)).hexdigest()


def check_hashes(password, hashed_text):
    if make_hashes(password) == hashed_text:
        return hashed_text
    return False


def display_section(title, key, description):
    show_section = st.checkbox(title, key=f"show_{key}")
    if show_section:
        st.info(description)
    return show_section


def show_footer():
    disclaimer_style = """
        <style>
        .disclaimer {
            color: #757575;
            font-size: 12px;
            text-align: center;
            margin-top: 20px;
        }
        </style>
    """
    st.markdown(disclaimer_style, unsafe_allow_html=True)
    st.markdown("<p class='disclaimer'>Please note that CaseBuilder is not intended to be used for legal purposes.</p>", unsafe_allow_html=True)


def set_dark_theme():
    dark_theme_css = """
    <style>
    :root {
        --primary-color: #1a1a1a;
        --background-color: #0e1117;
        --secondary-background-color: #161a23;
        --text-color: #ffffff;
        --font: sans-serif;
    }
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    </style>
    """
    st.markdown(dark_theme_css, unsafe_allow_html=True)


# Ensure the page icon works both locally and in cloud deployments

# Function to get base64 encoded image
def get_base64_encoded_image(image_path):
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

# Try to load the icon from various possible locations
icon_paths = [
    "logogearsmall.png",  # Current directory
    "./logogearsmall.png",  # Explicit current directory
    str(Path(__file__).parent / "logogearsmall.png"),  # Directory of this script
    "/app/logogearsmall.png",  # Common container path
]

# Try each path until we find the image
icon_data = None
for path in icon_paths:
    icon_data = get_base64_encoded_image(path)
    if icon_data:
        break

# Set page config with the icon (either as base64 or fallback to name)
if icon_data:
    st.set_page_config(
        page_title="CaseBuilder",
        page_icon=f"data:image/png;base64,{icon_data}",
        layout="wide",
        initial_sidebar_state="expanded"
    )
else:
    # Fallback to just using the name (might not work in cloud)
    st.set_page_config(
        page_title="CaseBuilder",
        page_icon="logogearsmall.png",
        layout="wide",
        initial_sidebar_state="expanded"
    )

set_dark_theme()

def display_sidebar_logo(width: int = 370):
    """Carga el logo en la barra lateral, centrado y con ancho fijo."""
    logo_paths = [
        "logocase.png",
        "./logocase.png",
        str(Path(__file__).parent / "logocase.png"),
        "/app/logocase.png",
    ]
    for path in logo_paths:
        if Path(path).exists():
            data = get_base64_encoded_image(path)
            if data:
                st.sidebar.markdown(
                    f"<div style='text-align:center;'><img src='data:image/png;base64,{data}' width='{width}px'></div>",
                    unsafe_allow_html=True
                )
            else:
                st.sidebar.warning("Logo image not found")
            return
    st.sidebar.warning("Logo image not found")

# (Logo display moved into show_sidebar for native centering)

@st.dialog("We'd Love Your Feedback")
def show_feedback_dialog():
    with st.form("feedback_form"):
        st.markdown("Let us know how we're doing:")

        # ⭐️ Star rating (using emojis + select_slider)
        rating = st.select_slider(
            "Rating",
            options=["⭐️", "⭐️⭐️", "⭐️⭐️⭐️", "⭐️⭐️⭐️⭐️", "⭐️⭐️⭐️⭐️⭐️"],
            value="⭐️⭐️⭐️⭐️⭐️"
        )

        feedback_type = st.radio("Type of Feedback", ["Bug", "Suggestion", "Comment"], horizontal=True)

        # Prefill name if logged in
        default_name = st.session_state.get('first_name', '') if st.session_state.get('loggedIn') else ''
        name = st.text_input("Name (optional)", value=default_name)

        comments = st.text_area("Your Feedback")
        email = st.text_input("Your Email (optional)")

        submitted = st.form_submit_button("Submit")
        if submitted:
            if comments and comments.strip():
                try:
                    insert_feedback(name, email, comments, rating, feedback_type)
                    st.success("✅ Thanks for your feedback!")
                    # Close the dialog after submission
                    st.session_state['show_feedback_dialog'] = False
                except Exception as e:
                    st.error(f"Error submitting feedback: {e}")
            else:
                st.error("Please enter your feedback before submitting.")

@st.dialog("Supplemental Case Information")
def show_input_dialog():
    # Add a close button to properly reset the dialog state
    if st.button("✕ Close Form", key="close_input_dialog"):
        st.session_state["show_dialog"] = False
        st.rerun()

    with st.form(key='InputForm'):

            client_name = st.text_input("Client Name:", value=st.session_state.get('client_name', ''), help="Enter the name of the client involved in the incident.")
            default_date = st.session_state.get('date_of_accident', None)
            date_of_accident = st.date_input("Date of Incident:", value=default_date, help="Select the date of the incident.")

            claim_number = st.text_input("Claim Number:", value=st.session_state.get('claim_number', ''), help="Enter the claim number associated with the incident.")
            insurance_company = st.text_input("Insurance Company:", value=st.session_state.get('insurance_company', ''), help="Enter the name of the insurance company.")
            adjuster_name = st.text_input("Adjuster Name:", value=st.session_state.get('adjuster_name', ''), help="Enter the name of the insurance adjuster.")

            injuries = st.text_area("Additional Injury Details:", value=st.session_state.get('injuries', ''),
                                help="Please provide any additional details about injuries that are not covered in the documents.")


            additional_form_notes = st.text_area("Additional Notes:", value="", placeholder="Enter any additional notes here...")

            submit_button = st.form_submit_button("Submit")
            if submit_button:
                st.session_state['claim_number'] = claim_number
                st.session_state['client_name'] = client_name

                st.session_state['date_of_accident'] = date_of_accident
                st.session_state['injuries'] = injuries
                st.session_state['insurance_company'] = insurance_company
                st.session_state['adjuster_name'] = adjuster_name

                updated_notes = ""

                if claim_number:
                    updated_notes += f"Claim Number: {claim_number}\n"
                if client_name:
                    updated_notes += f"Client Name: {client_name}\n"
                if injuries:
                    updated_notes += f"Injuries: {injuries}\n"
                if insurance_company:
                    updated_notes += f"Insurance Company: {insurance_company}\n"
                if adjuster_name:
                    updated_notes += f"Adjuster Name: {adjuster_name}\n"
                if additional_form_notes:
                    updated_notes += f"Additional Notes: {additional_form_notes}\n"

                existing_notes = st.session_state.get('additional_notes', "")
                new_notes = {}

                for line in existing_notes.splitlines():
                    if ": " in line:
                        key, value = line.split(": ", 1)
                        new_notes[key] = value

                for line in updated_notes.splitlines():
                    if ": " in line:
                        key, value = line.split(": ", 1)
                        new_notes[key] = value

                st.session_state['additional_notes'] = "\n".join([f"{key}: {value}" for key, value in new_notes.items()])
                st.session_state['show_dialog'] = False
                st.rerun()






@st.dialog("CaseBuilder Assistant")
def show_chat_assistant_dialog():
    """Display the chat assistant dialog."""
    # Add a close button to properly reset the dialog state
    if st.button("✕ Close Assistant", key="close_chat_dialog"):
        st.session_state["show_chat_dialog"] = False
        st.rerun()

    # Add custom CSS for the dialog title and content
    st.markdown("""
    <style>
    /* Ensure dialog has good contrast */
    .stDialog > div {
        background-color: #1E1E1E !important;
        color: #FFFFFF !important;
        border-radius: 12px !important;
        border: 1px solid #333333 !important;
    }
    .stDialog h1 {
        color: #FFFFFF !important;
        font-size: 24px !important;
        margin-bottom: 16px !important;
        border-bottom: 1px solid #333333 !important;
        padding-bottom: 8px !important;
    }
    /* Add some padding to the dialog content */
    .stDialog > div > div:nth-child(2) {
        padding: 0 10px !important;
    }
    /* Make the dialog scrollbar more visible */
    .stDialog > div > div:nth-child(2)::-webkit-scrollbar {
        width: 8px !important;
    }
    .stDialog > div > div:nth-child(2)::-webkit-scrollbar-track {
        background: #1E1E1E !important;
    }
    .stDialog > div > div:nth-child(2)::-webkit-scrollbar-thumb {
        background: #555555 !important;
        border-radius: 4px !important;
    }
    .stDialog > div > div:nth-child(2)::-webkit-scrollbar-thumb:hover {
        background: #777777 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    # Initialize chat history if it doesn't exist
    if 'chat_history' not in st.session_state:
        st.session_state['chat_history'] = [
            {"role": "assistant", "content": "Hi there! I'm your Assistant. How can I help you today?"}
        ]

    # Process any pending chat query
    if 'pending_chat_query' in st.session_state and 'is_processing_query' not in st.session_state:
        # Mark that we're processing a query to avoid reprocessing
        st.session_state['is_processing_query'] = True
        st.rerun()

    # If we're processing a query, show a spinner and process it
    if 'is_processing_query' in st.session_state and 'pending_chat_query' in st.session_state:
        user_input = st.session_state['pending_chat_query']

        # Show a spinner while generating the response
        with st.spinner("Generating response..."):
            # Use a synchronous wrapper to call the async function
            if 'response_ready' not in st.session_state:
                st.session_state['response_ready'] = False
                # Schedule the async task to be run in the background
                st.session_state['scheduled_task'] = True
                st.rerun()

            # If we've scheduled the task but don't have a response yet
            if 'scheduled_task' in st.session_state and not st.session_state['response_ready']:
                # This is a placeholder that will be replaced on the next rerun
                with st.spinner("Generating response..."):
                    # Create a placeholder for the response
                    if 'ai_response' not in st.session_state:
                        # Use a synchronous function to get the response
                        response = get_ai_response_sync(user_input)
                        st.session_state['ai_response'] = response
                        st.session_state['response_ready'] = True
                        del st.session_state['scheduled_task']
                        st.rerun()

            # If the response is ready, add it to the chat history
            if 'response_ready' in st.session_state and st.session_state['response_ready']:
                response = st.session_state['ai_response']

                # Add assistant response to chat history
                st.session_state['chat_history'].append({"role": "assistant", "content": response})

                # Clean up session state
                del st.session_state['pending_chat_query']
                del st.session_state['is_processing_query']
                del st.session_state['response_ready']
                del st.session_state['ai_response']
                st.rerun()

    # Display chat history
    for message in st.session_state['chat_history']:
        if message["role"] == "user":
            # User messages are always simple text
            content = message["content"]
            st.markdown(f"""
            <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
                <div style="background-color: #0078D7; padding: 10px; border-radius: 10px; max-width: 80%; color: white;">
                    <p style="margin: 0;"><strong>You:</strong> {content}</p>
                </div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Assistant messages might be dictionaries with HTML content
            if isinstance(message["content"], dict):
                content = message["content"]["content"]
                # Note: has_html is used to determine if content contains HTML that should be rendered
                # but we're always using unsafe_allow_html=True, so we don't need to check it here
            else:
                # Handle legacy format (string only)
                content = message["content"]

            # Create the message HTML with proper handling of HTML content
            message_html = f"""
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
                <div style="background-color: #22d3ee; padding: 10px; border-radius: 10px; max-width: 80%; color: #000000;">
                    <p style="margin: 0;"><strong>CaseBuilder Bot:</strong> {content}</p>
                </div>
            </div>
            """

            # Always use unsafe_allow_html=True to render the message
            # This allows HTML in responses that have HTML content
            st.markdown(message_html, unsafe_allow_html=True)

    # Chat input form
    with st.form(key="chat_form", clear_on_submit=True):
        # Add custom CSS for the input field
        st.markdown("""
        <style>
        /* Style the input field to match the dark theme of the app */
        .stTextInput input {
            color: #ffffff !important;
            background-color: #1e1e1e !important;
            border: 1px solid #333333 !important;
            border-radius: 4px !important;
            padding: 10px 12px !important;
            font-size: 16px !important;
            transition: all 0.3s ease !important;
        }
        .stTextInput input:focus {
            border-color: #22d3ee !important;
            box-shadow: 0 0 0 1px #22d3ee !important;
        }
        .stTextInput input::placeholder {
            color: #888888 !important;
        }
        /* Style the submit button to match */
        .stButton button {
            background-color: #22d3ee !important;
            color: #000000 !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 10px 20px !important;
            font-weight: bold !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
        }
        .stButton button:hover {
            background-color: #0cb7d3 !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-1px) !important;
        }
        /* Hide any labels */
        .stTextInput label {
            display: none !important;
        }
        </style>
        """, unsafe_allow_html=True)

        user_input = st.text_input("", placeholder="Enter your question...", key="user_question")
        submitted = st.form_submit_button("Send")

        if submitted and user_input:
            # Add user message to chat history
            st.session_state['chat_history'].append({"role": "user", "content": user_input})

            # Store the user input in session state for async processing
            st.session_state['pending_chat_query'] = user_input

            # Rerun to process the query asynchronously
            st.rerun()

def generate_assistant_response_with_predefined_answers(user_input):
    """Generate a response using predefined answers for common questions."""
    # Convert input to lowercase for easier matching
    input_lower = user_input.lower()

    # Check if this is a follow-up question based on chat history
    if 'chat_history' in st.session_state and len(st.session_state['chat_history']) >= 2:
        # Get the last assistant response and user query
        last_assistant_msg = None
        last_user_msg = None

        # Find the last assistant and user messages
        for msg in reversed(st.session_state['chat_history']):
            if msg["role"] == "assistant" and last_assistant_msg is None:
                last_assistant_msg = msg["content"]
                if isinstance(last_assistant_msg, dict) and "content" in last_assistant_msg:
                    last_assistant_msg = last_assistant_msg["content"]
            elif msg["role"] == "user" and last_user_msg is None:
                last_user_msg = msg["content"]

            # Once we have both, we can break
            if last_assistant_msg and last_user_msg:
                break

        # Check if this is a follow-up question based on the last assistant message
        if last_assistant_msg:
            # Handle follow-up questions about getting started
            if "getting started" in last_assistant_msg.lower() or "follow these 3 steps" in last_assistant_msg.lower():
                if "mandatory" in input_lower or "required" in input_lower or "necessary" in input_lower or "must" in input_lower or "every step" in input_lower:
                    return {"content": "The first step (setting preferences) is recommended but not mandatory. You can upload documents directly, but setting preferences first helps the system provide more accurate analysis. The second step (uploading documents) is necessary as the system needs documents to analyze. The third step (clicking to generate) is where you choose what type of analysis you want based on your uploaded documents.", "has_html": False}
                elif "step" in input_lower or "first" in input_lower or "second" in input_lower or "third" in input_lower:
                    return {"content": "For the first step (setting preferences), go to the sidebar and adjust case settings like state, case type, and analysis preferences. For the second step, simply drag and drop your documents into the upload area or click to browse your files. For the third step, once documents are uploaded, you'll see buttons appear that let you generate different types of analysis based on the document types you've uploaded.", "has_html": False}
                elif "preference" in input_lower or "setting" in input_lower:
                    return {"content": "In the preferences section, you can set your state, case type, and liability details. You can also configure how you want medical records analyzed (narrative, chronological, bulleted, or tabular format) and set demand letter preferences like tone (firm, neutral, conciliatory) and content emphasis. These settings help tailor the analysis to your specific needs.", "has_html": False}
                elif "upload" in input_lower or "document" in input_lower or "file" in input_lower:
                    return {"content": "To upload documents, simply drag and drop your files into the upload area or click to browse your files. CaseBuilder accepts PDFs (for medical records, police reports, and other documents) and images (JPG, PNG) for injury or accident scene photos. The system will automatically detect the document type and show you relevant analysis options.", "has_html": False}
                elif "button" in input_lower or "generate" in input_lower or "analysis" in input_lower:
                    return {"content": "After uploading documents, you'll see buttons appear based on the document types detected. For medical records, you'll see options like 'Medical Analysis', 'Medical Expenses', and 'Future Treatments'. For police reports, you'll see options like 'Facts & Liability Analysis' and 'In-Depth Liability Analysis'. For images, you'll see options like 'Analyze Injuries' or 'Property Damage'. When you have both medical records and police reports, you'll also see a 'Generate Demand Letter' button.", "has_html": False}

            # Handle follow-up questions about tokens and costs
            elif "token" in last_assistant_msg.lower() or "cost" in last_assistant_msg.lower():
                if "how" in input_lower or "buy" in input_lower or "purchase" in input_lower or "get" in input_lower:
                    return {"content": "You can purchase more tokens by clicking the 'Buy Tokens' button in the sidebar. This will take you to the pricing page where you can select a token package that fits your needs. After purchase, your token balance will be updated automatically and displayed in the sidebar.", "has_html": False}
                elif "expire" in input_lower or "valid" in input_lower or "time" in input_lower:
                    return {"content": "Tokens do not expire and remain valid until used. You can use them at any time for document analysis or demand letter generation.", "has_html": False}
                elif "refund" in input_lower or "return" in input_lower:
                    return {"content": "For questions about refunds or token returns, please contact our support team through the 'Contact Support' link in the sidebar.", "has_html": False}

            # Handle follow-up questions about document types
            elif "document" in last_assistant_msg.lower() or "upload" in last_assistant_msg.lower():
                if "format" in input_lower or "type" in input_lower:
                    return {"content": "CaseBuilder supports PDF files for documents (medical records, police reports, billing statements) and image files (JPG, PNG) for photos of injuries or accident scenes. The system automatically detects the document type upon upload.", "has_html": False}
                elif "size" in input_lower or "limit" in input_lower:
                    return {"content": "The maximum file size is 200MB per file. For optimal performance, we recommend keeping files under 50MB. If you have larger files, consider splitting them into smaller documents before uploading.", "has_html": False}
                elif "multiple" in input_lower or "several" in input_lower:
                    return {"content": "Yes, you can upload multiple documents. Simply drag and drop all files at once or select multiple files when browsing. You can also upload additional documents later as needed.", "has_html": False}

            # Handle follow-up questions about analysis
            elif "analysis" in last_assistant_msg.lower() or "analyze" in last_assistant_msg.lower():
                if "medical" in input_lower or "record" in input_lower:
                    return {"content": "Medical record analysis extracts key information like diagnoses, treatments, medications, and expenses. You can choose different output formats: narrative (prose), chronological (timeline), bulleted (list), or tabular (spreadsheet-like). The analysis also identifies ICD-10 codes when that option is selected.", "has_html": False}
                elif "police" in input_lower or "report" in input_lower or "liability" in input_lower:
                    return {"content": "Police report analysis extracts facts about the incident and provides liability analysis based on applicable laws. The in-depth liability analysis option gives a more comprehensive assessment that focuses on facts and legal principles rather than police opinions.", "has_html": False}
                elif "image" in input_lower or "photo" in input_lower or "picture" in input_lower:
                    return {"content": "Image analysis examines photos of injuries or accident scenes. For injuries, it provides detailed descriptions and relates them to the case context. For accident scenes, it analyzes vehicle positions, damages, and environmental factors to reconstruct the sequence of events.", "has_html": False}
                elif "accurate" in input_lower or "reliable" in input_lower:
                    return {"content": "CaseBuilder uses advanced AI models to ensure accurate analysis. For medical records, it uses specialized models trained on medical terminology. For police reports, it focuses on extracting factual information. The system is designed to minimize hallucinations and provide reliable, fact-based analysis.", "has_html": False}

    # Define common question patterns and their corresponding keywords
    question_patterns = {
        # Upload related questions
        r"(can|how) (i|to) upload": "upload",
        r"upload (multiple|several) (documents|files)": "upload",
        r"(multiple|several) (documents|files) at once": "upload",
        r"(how|can) (i|to) (add|submit) (documents|files)": "upload",

        # Security related questions
        r"(how|is) (it|data|information) (secure|protected|safe)": "security",
        r"(keep|protect) (my|client|clients|customer) data (secure|safe)": "security",
        r"(data|information) security": "security",
        r"secure": "security",
        r"(how|is) (my|the) (data|information) (handled|processed)": "security",

        # Privacy related questions
        r"(privacy|private|confidential)": "privacy",
        r"(how|do) you (handle|use|protect) (my|client|clients) (data|information)": "privacy",
        r"(what|how) (about|is) (my|client) (privacy|confidentiality)": "privacy",

        # Storage related questions
        r"(do|does|are) you (store|save|keep) (the|my) (documents|files|pdfs|images)": "store",
        r"(where|how) (are|do) (you|casebuilder) (store|save) (the|my) (documents|files|pdfs)": "store",
        r"(what happens|happen) (to|with) (my|the) (documents|files|pdfs) (after|when)": "store",
        r"(are|is) (my|the) (documents|files|pdfs) (stored|saved)": "store",
        r"(storage|retention) (of|for) (documents|files|pdfs)": "store",

        # Medical records related questions
        r"(analyze|analysis) (of|for) medical records": "medical_analysis",
        r"medical records (only|analysis)": "medical_analysis",
        r"(can|how) (i|to) (analyze|use) (this|it) (for|with) medical records": "medical_analysis",
        r"(what|how) (about|does) (the|your) medical (record|analysis) (work|function)": "medical_analysis",

        # Document types
        r"(what|which) (documents|files) (can|types|supported)": "document",
        r"(supported|acceptable) (document|file) (types|formats)": "document",
        r"(what|which) (kind|type) of (documents|files) (can|do) (i|you) (use|accept)": "document",
        r"(does|can|will) (it|the system|casebuilder) (recognize|detect|identify) (document|file) types": "document",
        r"(automatic|automatically) (recognize|detect|identify) (document|file) types": "document",

        # Analysis types
        r"(what|which|types of) analysis": "analysis",
        r"(can|how) (i|to) (analyze|get) (analysis)": "analysis",
        r"(what|how) (can|does) (casebuilder|it|the system) analyze": "analysis",

        # Demand letter
        r"(demand letter|generate letter)": "demand letter",
        r"(create|make|generate) (a|the) (demand|letter)": "demand letter",
        r"(how|what) (about|is) (the|a) demand letter": "demand letter",
        r"(what|how) (is|does) (included|contain) (in|the) (demand|the) letter": "demand letter",

        # Cost and tokens
        r"(cost|price|pricing|token|tokens)": "cost",
        r"(how much|fee|subscription)": "cost",
        r"(is|are) (there|any) (cost|fee|charge) (for|to) (chat|talking|using) (with|to) you": "cost",
        r"(do|does) (i|it) (cost|pay) (to|for) (use|using) (this|the) (chat|assistant)": "cost",

        # Demand letter token questions - these must come before general token patterns
        r"how many tokens (to|do) (build|generate|create|do|make|for) a demand letter": "demand_letter_tokens",
        r"how many tokens i need to do a demand letter": "demand_letter_tokens",
        r"how many tokens (do|does|would|will) (i|it) (need|take|cost|require) (to|for) (do|make|create|generate|build) (a|the) demand letter": "demand_letter_tokens",
        r"(how many|number of) tokens (for|to) (build|generate|create) (a|the) demand letter": "demand_letter_tokens",
        r"(how many|number of) tokens (does|do) (a|the) demand letter (cost|use|need|require)": "demand_letter_tokens",
        r"tokens (for|to) (build|generate|create) (a|the) demand letter": "demand_letter_tokens",
        r"(how many|number of) tokens (for|to|does|do) (a|the) demand letter": "demand_letter_tokens",

        # Chat related questions
        r"(what|who) (are|is) you": "chat",
        r"(how|what) (does|can) (this|the) (chat|assistant) (do|help with)": "chat",
        r"(tell me|explain) (about|what) (you|this chat) (can do|are)": "chat",

        # App functionality questions
        r"how (does|do) (this|the) (app|application|system|casebuilder) work": "app_functionality",
        r"how (to|can i|do i) use (this|the) (app|application|system|casebuilder)": "app_functionality",
        r"(what|explain) (is|does) (this|the) (app|application|system|casebuilder) (do|for)": "app_functionality",
        r"how (can|do) (i|to) get started": "getting_started",
        r"(where|how) (should|to|do) (i|we) begin": "getting_started",

        # Help and support
        r"(help|support|assistance)": "help",
        r"(how|where) (can|to) (i|get) help": "help",
        r"(where|how) (can|do) (i|to) (find|get) (more|additional) (help|information)": "help",

        # Feedback
        r"(feedback|suggest|suggestion)": "feedback",
        r"(report|bug|issue)": "feedback",
        r"(how|where) (can|do) (i|to) (provide|give|send) feedback": "feedback"
    }

    # Define responses for each keyword
    faq_responses = {
        "upload": "Yes, just drag and drop files into the upload area or click to browse. CaseBuilder accepts PDFs (medical records, police reports, bills) and images (JPG, PNG) of injuries or accident scenes.",

        "document": "Yes, CaseBuilder automatically detects document types on upload. It recognizes police reports, medical records, billing statements, and images, then shows only the relevant AI actions (e.g., medical-expense tables for bills, injury analysis for photos).",

        "medical_analysis": "Yes, CaseBuilder analyzes medical records to extract treatments, diagnoses, and expenses. You can choose different output formats: narrative (prose), chronological (timeline), bulleted (list), or tabular (spreadsheet-like).",

        "analysis": "CaseBuilder offers different analyses based on your documents: medical record summaries, police report liability analysis, expense calculations, future treatment projections, and injury image analysis.",

        "demand letter": "The demand letter combines all your case information into a professional document. It includes liability analysis, medical summary, expenses, lifestyle impact, and damages assessment. You can customize tone (firm/neutral/conciliatory), content emphasis, length, and formatting.",

        "preference": "You can customize CaseBuilder in the sidebar under 'Case Settings'. Set your state, case type, and liability details. You can also adjust analysis styles and demand letter formats to match your needs.",

        "error": "Try refreshing the page and uploading your documents again. Make sure your files are properly formatted PDFs or images. For persistent issues, contact support using the link in the sidebar.",

        "slow": "Processing time varies with document size and complexity. Large files may take several minutes. For faster results, try splitting large documents into smaller files before uploading.",

        "button": "CaseBuilder shows different action buttons based on your uploaded documents. Medical analysis buttons appear for medical records, police report buttons for police reports, and image analysis for photos.",

        "token": "Generating a demand letter costs 5 tokens. Other actions cost: Medical Analysis (1 token), Police Report Analysis (1 token), and Image Analysis (1 token per image). You can purchase more tokens through your account settings.",

        "demand_letter_tokens": "5 tokens. Generating a demand letter requires 5 tokens in CaseBuilder.",

        "cost": "Using the CaseBuilder Assistant chat feature is completely free. The main CaseBuilder application operates on a token-based system for document analysis and demand letter generation. You can see your remaining tokens in the sidebar when using the main application.",

        "help": "Check the 'How to Use CaseBuilder' and 'FAQ' sections in the sidebar for guidance. For additional help, use the 'Contact Support' link at the bottom of the sidebar.",

        "feedback": "We value your input! Use the 'We'd Love Your Feedback' link in the sidebar to share thoughts, report bugs, or suggest new features.",

        "code": "I'm sorry, but I can't provide information about CaseBuilder's internal code or implementation details. If you're experiencing technical issues, please contact our support team for assistance.",

        "api": "I'm sorry, but I can't provide information about CaseBuilder's API or backend systems. If you need to integrate with CaseBuilder, please contact our support team for official documentation and assistance.",

        "security": "CaseBuilder takes data security very seriously. We never permanently store your files. Documents are temporarily held in memory for processing, then immediately deleted after analysis is complete. Everything is encrypted in transit and processed securely. For more detailed information, please refer to our <a href='https://casebuilder.ai/security' target='_blank'>Data Security Policy</a>.",

        "privacy": "CaseBuilder is committed to protecting your privacy and your clients' privacy. We only collect information necessary to provide our services, never sell personal data to third parties, and comply with all relevant privacy regulations. For more detailed information, please refer to our <a href='https://casebuilder.ai/privacy' target='_blank'>Privacy Policy</a>.",

        "store": "No, CaseBuilder doesn't permanently store your documents. Files are temporarily held in memory during processing, then immediately deleted after analysis. This enhances privacy and security by minimizing data retention.",

        "chat": "I'm the CaseBuilder Assistant, here to answer questions about features, document analysis, demand letters, and more. This chat is completely free and doesn't use any tokens.",

        "app_functionality": "CaseBuilder helps legal professionals analyze documents and create demand letters. First, upload your documents (police reports, medical records, images). The AI will analyze them and provide summaries, expense calculations, and liability analysis. Finally, you can generate a professional demand letter that combines all this information. Each step uses tokens, with demand letters costing 5 tokens.",

        "getting_started": "Getting started with CaseBuilder is easy! Just follow these 3 steps: 1) Set your preferences in the sidebar (case settings, summary styles), 2) Upload your documents (drag and drop PDFs or images), and 3) Click the analysis buttons that appear. For medical records, you'll see medical analysis options. For police reports, you'll see liability analysis options. When you're ready, generate a demand letter with a single click."
    }

    # First, check for specific patterns about demand letter tokens and app functionality
    import re
    for pattern in [
        r"how many tokens (to|do) (build|generate|create|do|make|for) a demand letter",
        r"how many tokens i need to do a demand letter",
        r"how many tokens (do|does|would|will) (i|it) (need|take|cost|require) (to|for) (do|make|create|generate|build) (a|the) demand letter",
        r"how this app works",
        r"how can i get started",
        r"how to use"
    ]:
        if re.search(pattern, input_lower):
            # For demand letter token questions
            if "token" in pattern:
                response = faq_responses.get("demand_letter_tokens")
                if response:
                    return {"content": response, "has_html": False}
            # For app functionality questions
            elif "how this app works" in pattern and "how this app works" in input_lower:
                response = faq_responses.get("app_functionality")
                if response:
                    return {"content": response, "has_html": False}
            # For getting started questions
            elif (("how can i get started" in pattern and "how can i get started" in input_lower) or
                  ("how to use" in pattern and "how to use" in input_lower)):
                response = faq_responses.get("getting_started")
                if response:
                    return {"content": response, "has_html": False}

    # Then check for other pattern matches
    for pattern, keyword in question_patterns.items():
        if re.search(pattern, input_lower):
            response = faq_responses.get(keyword)
            if response:
                # Check if this is a response that contains HTML
                if "<a href=" in response:
                    return {"content": response, "has_html": True}
                else:
                    return {"content": response, "has_html": False}

    # If no pattern match, check for exact keyword matches
    for keyword, response in faq_responses.items():
        if keyword in input_lower:
            # Check if this is a response that contains HTML (like security or privacy)
            if "<a href=" in response:
                # Mark that this response contains HTML that should be rendered
                return {"content": response, "has_html": True}
            else:
                return {"content": response, "has_html": False}

    # Handle greetings
    greetings = ["hi", "hello", "hey", "greetings", "howdy"]
    for greeting in greetings:
        if greeting in input_lower or input_lower == greeting:
            return {"content": "Hello! How can I help you with CaseBuilder today?", "has_html": False}

    # Handle thank you
    thank_yous = ["thank", "thanks", "appreciate", "grateful"]
    for thank in thank_yous:
        if thank in input_lower:
            return {"content": "You're welcome! Is there anything else I can help you with?", "has_html": False}

    # Handle goodbyes
    goodbyes = ["bye", "goodbye", "see you", "farewell"]
    for goodbye in goodbyes:
        if goodbye in input_lower:
            return {"content": "Goodbye! Feel free to ask if you have more questions about CaseBuilder in the future.", "has_html": False}

    # For other questions, return a generic response
    return {"content": "I'm here to help with questions about CaseBuilder. You can ask about document uploads, analysis features, demand letters, or check the FAQ section in the sidebar for more information.", "has_html": False}

def get_ai_response_sync(user_input):
    """Generate a response to the user's question about CaseBuilder."""
    # Try to use the AssistantAgent if available, otherwise fall back to predefined answers
    try:
        from casebuilder.application.commands.assistant_commands import AssistantQueryCommand
        from casebuilder.application.handlers.assistant_handler import AssistantQueryHandler
        from casebuilder.infrastructure.dependency_injection import ServiceLocator

        # Get services from the service locator
        ai_service = ServiceLocator.get_service("ai_service")
        text_processor = ServiceLocator.get_service("text_processor")

        if ai_service and text_processor:
            # Get predefined answers from the function that generates them
            predefined_response = generate_assistant_response_with_predefined_answers(user_input)
            if isinstance(predefined_response, dict) and "content" in predefined_response:
                predefined_content = predefined_response["content"]
            else:
                predefined_content = str(predefined_response)

            # Create a simple predefined answers dictionary with just this response
            predefined_answers = {"exact_match": {"content": predefined_content}}
            handler = AssistantQueryHandler(ai_service, text_processor, predefined_answers)

            # Create the command with the user input and chat history
            chat_history = st.session_state.get('chat_history', [])
            command = AssistantQueryCommand(
                query_text=user_input,
                chat_history=chat_history,
                app_state={
                    "documents_uploaded": st.session_state.get('documents_uploaded', False),
                    "document_types": st.session_state.get('document_types', []),
                    "case_type": st.session_state.get('case_type', None),
                    "selected_state": st.session_state.get('selected_state', None)
                }
            )

            # Process the command synchronously (we're in a sync context)
            import asyncio
            loop = asyncio.new_event_loop()
            response = loop.run_until_complete(handler.handle(command))
            loop.close()

            # Return the response in the expected format
            return {"content": response, "has_html": "<a href=" in response}
        else:
            # Fall back to predefined answers if services aren't available
            return generate_assistant_response_with_predefined_answers(user_input)
    except Exception as e:
        print(f"Error using AssistantAgent: {e}")
        # Fall back to predefined answers if there's an error
        return generate_assistant_response_with_predefined_answers(user_input)

async def main():
    if 'loggedIn' not in st.session_state:
        # Página de login: mostrar logo en sidebar
        display_sidebar_logo(width=370)
        st.session_state['loggedIn'] = False
        st.session_state['username'] = None
        show_login_page()
    else:
        if st.session_state['loggedIn']:
            st.session_state['cancel_process'] = False

            apply_button_style()

            if 'documents_uploaded' not in st.session_state:
                st.session_state['documents_uploaded'] = False

            # Only show the warning if no documents have been uploaded yet
            # Check if any document types exist in session state (except 'image')
            has_documents = 'document_types' in st.session_state and any(
                doc_type in st.session_state['document_types']
                for doc_type in ['police_report', 'medical_record', 'other']
            )

            if not st.session_state['documents_uploaded'] and not has_documents:
                st.warning("Please upload the necessary documents to get started.")

            show_sidebar()

            # Show chat dialog if triggered
            if st.session_state.get("show_chat_dialog", False):
                try:
                    show_chat_assistant_dialog()
                except Exception as e:
                    # Handle the StreamlitAPIException gracefully
                    if "Only one dialog is allowed to be opened at the same time" in str(e):
                        st.warning("⚠️ Streamlit only allows one dialog at a time. Please close the other dialog using its close button, or refresh the page to continue.")
                        # Reset the dialog state to avoid getting stuck
                        st.session_state['show_chat_dialog'] = False
                    else:
                        # Re-raise other exceptions
                        raise e

            await show_unified_document_section()
            show_footer()
        else:
            # Usuario no autenticado: mostrar logo y form de login
            display_sidebar_logo(width=370)
            show_login_page()


if __name__ == "__main__":
    asyncio.run(main())
