import tiktoken
import asyncio
import logging
from typing import List, Optional, Dict, Any, Tuple

from casebuilder.core.interfaces.ITextProcessorService import ITextProcessorService
from casebuilder.core.interfaces.IAIService import IAIService


class TextProcessorService(ITextProcessorService):
    """
    Service for processing large text documents.
    Implements the ITextProcessorService interface.

    This service handles:
    - Splitting large texts into manageable segments
    - Processing each segment with an AI model
    - Combining the results into a coherent whole
    - Optimizing token usage for different models

    It maintains compatibility with the original text_processing.py module
    while providing a cleaner, more robust implementation.
    """

    # Default token limits for different models
    MAX_TOTAL_TOKENS_MAP = {
        # GPT-4o models
        "gpt4o": 128000,
        "gpt4omini": 128000,
        "gpt-4o": 128000,
        "gpt-4o-mini": 128000,
        "gpt-4o-2024-05-13": 128000,

        # o4-mini model
        "o4-mini": 128000,

        # Legacy model aliases
        "o1": 128000,
        "o3": 200000,
        "o1-mini": 128000,
        "o3-mini": 200000
    }

    MAX_COMPLETION_TOKENS_MAP = {
        # GPT-4o models
        "gpt4o": 16384,
        "gpt4omini": 16384,
        "gpt-4o": 16384,
        "gpt-4o-mini": 16384,
        "gpt-4o-2024-05-13": 16384,

        # o4-mini model
        "o4-mini": 16384,

        # Legacy model aliases
        "o1": 16384,
        "o3": 16384,
        "o1-mini": 16384,
        "o3-mini": 16384
    }

    def __init__(self, ai_service: IAIService):
        """
        Initialize the text processor service.

        Args:
            ai_service: Service for processing text with AI models
        """
        self.ai_service = ai_service
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        self.logger = logging.getLogger(__name__)

    def split_text_into_segments(self, text: str, max_tokens: int = 8000) -> List[str]:
        """
        Split a large text into smaller segments based on token count.

        Args:
            text: The text to split
            max_tokens: Maximum number of tokens per segment

        Returns:
            List of text segments
        """
        if not text:
            return []

        tokens = self.tokenizer.encode(text)

        segments = []
        current_segment = []
        current_tokens = 0

        for token in tokens:
            current_segment.append(token)
            current_tokens += 1

            if current_tokens >= max_tokens:
                segments.append(self.tokenizer.decode(current_segment))
                current_segment = []
                current_tokens = 0

        # Add any remaining tokens as the final segment
        if current_segment:
            segments.append(self.tokenizer.decode(current_segment))

        self.logger.info(f"Split text into {len(segments)} segments")
        return segments

    def _calculate_tokens(self, text: str) -> int:
        """
        Calculate the number of tokens in a text.

        Args:
            text: The text to calculate tokens for

        Returns:
            Number of tokens
        """
        return len(self.tokenizer.encode(text))

    def _calculate_available_tokens(
        self,
        model_alias: str,
        prompt_tokens: int,
        segment_tokens: int,
        max_completion_tokens: Optional[int] = None
    ) -> Tuple[int, int]:
        """
        Calculate available tokens for a model and segment.

        Args:
            model_alias: Model identifier
            prompt_tokens: Number of tokens in the prompt
            segment_tokens: Number of tokens in the segment
            max_completion_tokens: Maximum tokens for completion

        Returns:
            Tuple containing:
            - Maximum tokens for completion
            - Available tokens for completion
        """
        # Get model limits
        max_total = self.MAX_TOTAL_TOKENS_MAP.get(model_alias, 100000)
        max_completion = self.MAX_COMPLETION_TOKENS_MAP.get(model_alias, 50000)

        # Apply custom limit if provided
        if max_completion_tokens:
            max_completion = min(max_completion, max_completion_tokens)

        # Calculate available tokens with safety margin
        available_for_completion = min(
            max_completion,
            max_total - prompt_tokens - segment_tokens - 1000  # 1000 token safety margin
        )

        return max_completion, available_for_completion

    async def process_text_in_segments(
        self,
        model_alias: str,
        text: str,
        prompt: str,
        max_completion_tokens: Optional[int] = None
    ) -> str:
        """
        Process a large text by splitting it into segments.
        Process each segment with an AI model and combine the results.

        Args:
            model_alias: Identifier for the AI model to use
            text: The text to process
            prompt: The prompt or instruction for the AI model
            max_completion_tokens: Maximum tokens for completion

        Returns:
            Combined processed text result

        Raises:
            ValueError: If the model alias is unknown
            RuntimeError: If processing fails
        """
        # Validate inputs
        if not text:
            self.logger.warning("Empty text provided to process_text_in_segments")
            return ""

        if not prompt:
            self.logger.warning("Empty prompt provided to process_text_in_segments")
            return ""

        # Count prompt tokens
        prompt_tokens = self._calculate_tokens(prompt)
        self.logger.info(f"Prompt tokens: {prompt_tokens}")

        # Split text into segments
        text_segments = self.split_text_into_segments(text, max_tokens=8000)
        if not text_segments:
            self.logger.warning("No segments to process")
            return ""

        # Process segments in parallel with increased concurrency
        # Use more concurrent tasks for smaller models
        if model_alias in ["o4-mini", "gpt-4o-mini", "gpt4omini"]:
            concurrency = 10  # Higher concurrency for smaller models
        else:
            concurrency = 5   # Lower concurrency for larger models

        semaphore = asyncio.Semaphore(concurrency)
        self.logger.info(f"Processing {len(text_segments)} segments with concurrency {concurrency}")
        summaries = []

        async def process_segment(segment: str) -> Optional[str]:
            # Calculate tokens and available space
            segment_tokens = self._calculate_tokens(segment)
            _, available_for_completion = self._calculate_available_tokens(
                model_alias, prompt_tokens, segment_tokens, max_completion_tokens
            )

            if available_for_completion <= 0:
                self.logger.warning(f"Skipping segment with {segment_tokens} tokens due to insufficient tokens for completion")
                return None

            # Process segment with rate limiting
            async with semaphore:
                try:
                    result = await self.ai_service.process_text(
                        model_alias=model_alias,
                        system_prompt=prompt,
                        user_content=f"extract_information('{segment}')",
                        max_tokens=available_for_completion
                    )
                    return result
                except Exception as e:
                    self.logger.error(f"Error processing segment: {e}")
                    return None

        # Create tasks for all segments and process in parallel
        tasks = [process_segment(segment) for segment in text_segments]
        results = await asyncio.gather(*tasks)

        # Filter out None results
        summaries = [result for result in results if result is not None]
        if not summaries:
            self.logger.error("No segments were successfully processed")
            return ""

        # Combine all segment summaries
        full_text = " ".join(summaries)

        # For short texts, skip consolidation
        if len(text_segments) <= 1:
            return full_text

        # Final consolidation to remove duplicates
        consolidation_prompt = (
            f"Consolidate and remove any duplicate information from the following text:\n'{full_text}'"
        )

        # Calculate available tokens for final summary
        consolidation_tokens = self._calculate_tokens(consolidation_prompt)
        max_completion, final_summary_tokens = self._calculate_available_tokens(
            model_alias, prompt_tokens, consolidation_tokens, max_completion_tokens
        )

        self.logger.info(f"Consolidating with {consolidation_tokens} tokens, available: {final_summary_tokens}")

        if final_summary_tokens <= 0:
            self.logger.warning("Insufficient tokens for consolidation, returning concatenated summaries")
            return full_text

        try:
            final_summary = await self.ai_service.process_text(
                model_alias=model_alias,
                system_prompt=prompt,
                user_content=consolidation_prompt,
                max_tokens=final_summary_tokens
            )
            return final_summary
        except Exception as e:
            self.logger.error(f"Error during consolidation: {e}")
            return full_text

    # Legacy compatibility method
    async def process_text_in_segments_o1(self, text: str, prompt: str) -> str:
        """
        Legacy compatibility method for original text_processing.py.

        Args:
            text: The text to process
            prompt: The prompt or instruction

        Returns:
            Processed text
        """
        return await self.process_text_in_segments("o1", text, prompt)

    # Legacy compatibility method
    async def process_text_in_segments_o3(self, text: str, prompt: str) -> str:
        """
        Legacy compatibility method for original text_processing.py.

        Args:
            text: The text to process
            prompt: The prompt or instruction

        Returns:
            Processed text
        """
        return await self.process_text_in_segments("o3", text, prompt)