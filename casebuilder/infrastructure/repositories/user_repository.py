import os
import mysql.connector
from typing import List, Optional, Tuple, Any, Dict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from casebuilder.core.models.User import User, TokenInfo


class UserRepository:
    """
    Repository for user-related database operations.
    Adapts the existing user.py functionality to the new architecture.
    """

    def __init__(
        self,
        host: Optional[str] = None,
        port: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        database: Optional[str] = None
    ):
        """
        Initialize the user repository.

        Args:
            host: Database host (defaults to environment variable)
            port: Database port (defaults to environment variable)
            user: Database user (defaults to environment variable)
            password: Database password (defaults to environment variable)
            database: Database name (defaults to environment variable)
        """
        # Get database credentials from environment variables
        self.host = host or os.getenv('DATABASE_HOST')
        self.port = port or os.getenv('DATABASE_PORT')
        self.user = user or os.getenv('DATABASE_USER')
        self.password = password or os.getenv('DATABASE_PASSWORD')
        self.database = database or os.getenv('DATABASE_NAME')

        # Validate required parameters
        if not all([self.host, self.port, self.user, self.password, self.database]):
            missing = []
            if not self.host: missing.append("DATABASE_HOST")
            if not self.port: missing.append("DATABASE_PORT")
            if not self.user: missing.append("DATABASE_USER")
            if not self.password: missing.append("DATABASE_PASSWORD")
            if not self.database: missing.append("DATABASE_NAME")

            raise ValueError(f"Missing required database configuration: {', '.join(missing)}")

    def _get_connection(self):
        """
        Get a database connection.
        Uses the same logic as the legacy get_con() function for compatibility.

        Returns:
            MySQL connection object

        Raises:
            RuntimeError: If connection fails
        """
        try:
            # Use the same connection logic as the legacy get_con() function
            db_user = self.user or os.getenv("DATABASE_USER")
            db_pass = self.password or os.getenv("DATABASE_PASSWORD")
            db_name = self.database or os.getenv("DATABASE_NAME")

            # Detecta si estamos en Cloud Run o en local
            if os.getenv("INSTANCE_CONNECTION_NAME"):
                # Estamos en producción usando socket
                db_socket_dir = os.getenv("DB_SOCKET_DIR", "/cloudsql")
                cloud_sql_connection_name = os.getenv("INSTANCE_CONNECTION_NAME")

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    unix_socket=f"{db_socket_dir}/{cloud_sql_connection_name}"
                )
            else:
                # Estamos en local usando IP y puerto
                db_host = self.host or os.getenv("DATABASE_HOST", "127.0.0.1")
                db_port = int(self.port or os.getenv("DATABASE_PORT", 3306))

                conn = mysql.connector.connect(
                    user=db_user,
                    password=db_pass,
                    database=db_name,
                    host=db_host,
                    port=db_port
                )
            return conn
        except mysql.connector.Error as e:
            raise RuntimeError(f"Database connection error: {e}")

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        Authenticate a user with username and password.

        Args:
            username: User's username
            password: User's password

        Returns:
            User object if authentication successful, None otherwise

        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute query
            cursor.execute(
                'SELECT * FROM user WHERE username = %s AND password = %s',
                (username, password)
            )

            # Fetch result
            result = cursor.fetchone()

            # Close resources
            cursor.close()
            conn.close()

            # Return User object if authentication successful
            if result:
                # Assuming columns: id, username, password, first_name, last_name, email
                user = User(
                    username=result[1],
                    first_name=result[3],
                    last_name=result[4] if len(result) > 4 else None,
                    email=result[5] if len(result) > 5 else None,
                    user_id=result[0]
                )

                # Update last login timestamp after successful authentication
                await self.update_last_login(username)

                return user

            return None

        except Exception as e:
            raise RuntimeError(f"Authentication error: {e}")

    async def get_token_info(self, username: str, button_name: str) -> Optional[TokenInfo]:
        """
        Get token information for a user and button.

        Args:
            username: User's username
            button_name: The button/action name

        Returns:
            TokenInfo object if found, None otherwise

        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute query
            cursor.execute(
                'SELECT * FROM button_clicks WHERE username = %s AND button_name = %s',
                (username, button_name)
            )

            # Fetch result
            result = cursor.fetchone()

            # Close resources
            cursor.close()
            conn.close()

            # Return TokenInfo object if found
            if result:
                # Assuming columns: id, username, button_name, click_count, limit_count
                token_info = TokenInfo(
                    button_id=result[0],
                    username=result[1],
                    button_name=result[2],
                    click_count=result[3],
                    limit_count=result[4]
                )
                return token_info

            return None

        except Exception as e:
            raise RuntimeError(f"Error getting token info: {e}")

    async def update_token_usage(self, button_id: int, new_count: int) -> bool:
        """
        Update token usage for a button.

        Args:
            button_id: The button ID
            new_count: The new click count

        Returns:
            True if update successful, False otherwise

        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute update
            cursor.execute(
                'UPDATE button_clicks SET click_count = %s WHERE id = %s',
                (new_count, button_id)
            )

            # Commit changes
            conn.commit()

            # Check if update was successful
            success = cursor.rowcount > 0

            # Close resources
            cursor.close()
            conn.close()

            return success

        except Exception as e:
            raise RuntimeError(f"Error updating token usage: {e}")

    async def create_token_allocation(self, username: str, button_name: str, initial_limit: int = 10) -> TokenInfo:
        """
        Create a new token allocation for a user.

        Args:
            username: User's username
            button_name: The button/action name
            initial_limit: Initial token limit

        Returns:
            New TokenInfo object

        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Execute insert
            cursor.execute(
                'INSERT INTO button_clicks (username, button_name, click_count, limit_count) VALUES (%s, %s, %s, %s)',
                (username, button_name, 0, initial_limit)
            )

            # Get the new ID
            button_id = cursor.lastrowid

            # Commit changes
            conn.commit()

            # Close resources
            cursor.close()
            conn.close()

            # Return new TokenInfo
            return TokenInfo(
                button_id=button_id,
                username=username,
                button_name=button_name,
                click_count=0,
                limit_count=initial_limit
            )

        except Exception as e:
            raise RuntimeError(f"Error creating token allocation: {e}")

    async def update_last_login(self, username: str) -> bool:
        """
        Update the last_login timestamp for a user.
        Creates the last_login column if it doesn't exist.

        Args:
            username: The username to update

        Returns:
            True if update successful, False otherwise

        Raises:
            RuntimeError: If database operation fails
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Ensure last_login column exists
            cursor.execute(
                """
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = DATABASE() AND table_name = 'user' AND column_name = 'last_login'
                """
            )
            if cursor.fetchone()[0] == 0:
                cursor.execute("ALTER TABLE user ADD COLUMN last_login TIMESTAMP NULL")

            # Update the last_login timestamp with GMT-7 timezone
            cursor.execute('UPDATE user SET last_login = CONVERT_TZ(NOW(), @@session.time_zone, "-07:00") WHERE username = %s', (username,))

            # Commit changes
            conn.commit()

            # Check if update was successful
            success = cursor.rowcount > 0

            # Close resources
            cursor.close()
            conn.close()

            return success

        except Exception as e:
            raise RuntimeError(f"Error updating last login: {e}")