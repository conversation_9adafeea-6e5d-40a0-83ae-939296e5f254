from typing import Dict, Any, Optional, List

from ..commands.demand_letter_commands import (
    GeneralDamagesAnalysisCommand,
    ImpactOnLifestyleAnalysisCommand,
    GenerateDemandLetterCommand
)
from .command_handler import <PERSON>Handler
from ...core.interfaces.IAIService import IAIService
from ...core.interfaces.ITextProcessorService import ITextProcessorService

class GeneralDamagesAnalysisHandler(CommandHandler[str]):
    """
    Handler for generating a general damages analysis.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: GeneralDamagesAnalysisCommand) -> str:
        """
        Process the provided information to generate a general damages analysis.

        Args:
            command: The general damages analysis command

        Returns:
            A general damages analysis as a formatted string
        """
        # Generate the prompt for analysis
        prompt = self._create_damages_prompt(command)

        # Combine available text for analysis
        combined_text = self._combine_text_for_analysis(command)

        # Process text through text processor
        result = await self.text_processor.process_text_in_segments(
            model_alias="o4-mini",  # Using o4-mini
            text=combined_text,
            prompt=prompt
        )

        return result

    def _combine_text_for_analysis(self, command: GeneralDamagesAnalysisCommand) -> str:
        """
        Combine available text from different sources for comprehensive analysis.

        Args:
            command: The general damages analysis command

        Returns:
            Combined text as a string
        """
        parts = []

        if command.medical_analysis:
            parts.append("=== MEDICAL ANALYSIS ===")
            parts.append(command.medical_analysis)

        if command.medical_expenses:
            parts.append("=== MEDICAL EXPENSES ===")
            parts.append(command.medical_expenses)

        if command.future_expenses:
            parts.append("=== FUTURE MEDICAL EXPENSES ===")
            parts.append(command.future_expenses)

        if command.document_text:
            parts.append("=== ADDITIONAL DOCUMENT TEXT ===")
            parts.append(command.document_text)

        return "\n\n".join(parts)

    def _create_damages_prompt(self, command: GeneralDamagesAnalysisCommand) -> str:
        """
        Create the prompt for general damages analysis based on the command.

        Args:
            command: The general damages analysis command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''

        prompt = f"""
            As an AI legal expert specializing in personal injury damages calculation, your task is to analyze the case materials and provide a detailed itemized breakdown of General Damages sustained by {client_name}.

            **CRITICAL REQUIREMENT: You must provide your analysis in the following EXACT format:**

            **GENERAL DAMAGES ANALYSIS**

            1. **Pain and Suffering**
            • Facts: [Extract specific facts from the case materials about physical pain, discomfort levels, duration, treatments, etc.]
            • Estimate: $[specific dollar amount]

            2. **Emotional Distress**
            • Facts: [Extract specific facts about anxiety, depression, PTSD, sleep issues, mental health impacts, etc.]
            • Estimate: $[specific dollar amount]

            3. **Loss of Enjoyment of Life**
            • Facts: [Extract specific facts about activities, hobbies, sports, social activities that were abandoned or limited]
            • Estimate: $[specific dollar amount]

            4. **Loss of Consortium** (if applicable)
            • Facts: [Extract specific facts about impact on marriage, intimacy, family relationships, household duties]
            • Estimate: $[specific dollar amount]

            5. **Duties Under Duress**
            • Facts: [Extract specific facts about performing necessary activities despite pain, household tasks, work duties performed while injured]
            • Estimate: $[specific dollar amount]

            6. **Future Psychological Care** (if applicable)
            • Facts: [Extract facts about ongoing therapy needs, projected treatment duration, mental health prognosis]
            • Estimate: $[specific dollar amount]

            7. **Disfigurement** (if applicable)
            • Facts: [Extract specific facts about scarring, permanent physical changes, cosmetic impacts]
            • Estimate: $[specific dollar amount]

            8. **Loss of Income (Non-economic component)** (if applicable)
            • Facts: [Extract specific facts about time off work, reduced productivity, career impact beyond pure economic loss]
            • Estimate: $[specific dollar amount]

            **Total Estimated General Damages: $[sum of all categories]**

            **INSTRUCTIONS FOR DAMAGE CALCULATION:**

            1. **Extract Facts Carefully**: For each category, identify specific facts from the medical records, case materials, and expenses that support that type of damage.

            2. **Calculate Realistic Amounts**: Base your estimates on:
               - Severity and duration of the condition
               - Impact on daily life and activities
               - Age and life expectancy of the client
               - Typical compensation ranges for similar injuries
               - Geographic location and local jury verdicts

            3. **Only Include Applicable Categories**: If a category doesn't apply to this case (e.g., no disfigurement, no spouse for consortium), omit that section entirely.

            4. **Be Specific with Facts**: Don't use generic language. Extract actual details from the case materials such as:
               - Specific pain levels (e.g., "7-8/10 pain initially, now 3-5/10")
               - Specific activities affected (e.g., "unable to play weekend soccer for over a year")
               - Specific timeframes (e.g., "three weeks completely off work")
               - Specific treatments (e.g., "multiple invasive injections")

            5. **Justify Each Amount**: Each estimate should be reasonable and proportionate to the facts presented.

            **Additional Notes (if applicable):**
            {additional_notes}

            **Remember**: Your output must follow the exact format shown above with numbered categories, bullet points for Facts and Estimate, and a total at the end. Do NOT provide a summary or different format.
        """

        return prompt


class ImpactOnLifestyleAnalysisHandler(CommandHandler[str]):
    """
    Handler for analyzing the impact on lifestyle from case information.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: ImpactOnLifestyleAnalysisCommand) -> str:
        """
        Process the provided information to analyze the impact on lifestyle.

        Args:
            command: The impact on lifestyle analysis command

        Returns:
            A lifestyle impact analysis as a formatted string
        """
        # Generate the prompt for analysis
        prompt = self._create_lifestyle_prompt(command)

        # Combine available text for analysis
        combined_text = self._combine_text_for_analysis(command)

        # Process text through text processor
        result = await self.text_processor.process_text_in_segments(
            model_alias="o4-mini",  # Using o4-mini
            text=combined_text,
            prompt=prompt
        )

        return result

    def _combine_text_for_analysis(self, command: ImpactOnLifestyleAnalysisCommand) -> str:
        """
        Combine available text from different sources for comprehensive analysis.

        Args:
            command: The impact on lifestyle analysis command

        Returns:
            Combined text as a string
        """
        parts = []

        if command.medical_analysis:
            parts.append("=== MEDICAL ANALYSIS ===")
            parts.append(command.medical_analysis)

        if command.document_text:
            parts.append("=== ADDITIONAL DOCUMENT TEXT ===")
            parts.append(command.document_text)

        return "\n\n".join(parts)

    def _create_lifestyle_prompt(self, command: ImpactOnLifestyleAnalysisCommand) -> str:
        """
        Create the prompt for impact on lifestyle analysis based on the command.

        Args:
            command: The impact on lifestyle analysis command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''

        # This is a simplified version of the prompt from case_builder.py
        prompt = f"""
            As an AI legal expert, assess the impact of the injuries sustained by {client_name} on their lifestyle and daily activities. Your narrative should cover the changes in the client's quality of life due to the incident, including but not limited to emotional distress, inability to perform daily tasks, and any alterations to the client's relationships or recreational activities.

            **Key Points:**
            - Identify specific lifestyle changes that the client has experienced as a direct result of the incident.
            - Discuss emotional and psychological effects that may have arisen due to the injuries.
            - Emphasize how these lifestyle changes justify the compensation amount being sought in the demand letter.

            Detail the significant changes in the client's daily routine, including limitations on physical activities, social interactions, and emotional well-being.
            Discuss any psychological effects that impact the client's ability to enjoy life and engage in previously enjoyed activities.
            Argue for the necessity of considering these lifestyle impacts as part of the compensation claim.

            **Additional Notes (if applicable):**
            {additional_notes}
        """

        return prompt


class GenerateDemandLetterHandler(CommandHandler[str]):
    """
    Handler for generating a complete demand letter based on all case information.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: GenerateDemandLetterCommand) -> str:
        """
        Process all provided information to generate a complete demand letter.

        Args:
            command: The generate demand letter command with all case information

        Returns:
            A complete demand letter as a formatted string
        """
        # Consolidate all information for the demand letter
        consolidated_info = self._consolidate_info(command)

        # Generate the prompt for the demand letter
        prompt = self._create_demand_letter_prompt(command, consolidated_info)

        # Process text through text processor
        # First do initial processing with smaller model for better performance
        initial_result = await self.text_processor.process_text_in_segments(
            model_alias="o4-mini",  # Using smaller model for initial processing
            text=consolidated_info,
            prompt=f"Analyze and organize the following information for a demand letter: {consolidated_info}"
        )

        # Then use the larger model for the final demand letter generation
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt4o",  # Using gpt-4o for final quality
            text=initial_result,  # Use the processed result from the smaller model
            prompt=prompt
        )

        return result

    def _consolidate_info(self, command: GenerateDemandLetterCommand) -> str:
        """
        Consolidate all case information for comprehensive analysis.

        Args:
            command: The generate demand letter command

        Returns:
            Consolidated information as a string
        """
        info_parts = [
            f"State: {command.selected_state}",
            f"Liability Type: {command.liability_type}",
            f"Loss of Income: {command.loss_of_income or ''}",
            f"Demand Amount: {command.demand_amount}",
            f"Due Date: {command.due_date or 'No Date Set'}",
            f"UM Amount Requested: ${command.um_amount}" if command.liability_type in ['Uninsured Motorist', 'Under-Insured Motorist'] and command.um_amount else "",
            f"Liability Accepted: {'Yes' if command.liability_accepted else 'No'}",
            f"Liability Percentage: {command.liability_percentage}%",
            "Additional Notes:",
            command.additional_notes or '',
            "Facts of Loss & Liability:",
            command.facts_and_liability or '',
            "Medical Information:",
            command.medical_analysis or '',
            "Medical Expenses",
            command.current_medical_expenses or '',
            "Future Treatments:",
            command.future_medical_expenses or '',
            "General Damages:",
            command.general_damages or '',
            "Lifestyle Impact:",
            command.impact_on_lifestyle or '',
            "Legal Framework:",
            command.legal_framework or ''
        ]

        # Add image analysis if available
        if command.image_analysis_result:
            info_parts.extend([
                "Injury Documentation (from Images):",
                command.image_analysis_result
            ])

        if command.accident_scene_analysis:
            info_parts.extend([
                "Accident Scene Analysis (from Images):",
                command.accident_scene_analysis
            ])

        if command.property_damage_analysis:
            info_parts.extend([
                "Property Damage Analysis (from Images):",
                command.property_damage_analysis
            ])

        consolidated_info = "\n".join(part for part in info_parts if part)
        return consolidated_info

    def _create_demand_letter_prompt(self, command: GenerateDemandLetterCommand, consolidated_info: str) -> str:
        """
        Create the prompt for generating a demand letter based on the command and consolidated info.

        Args:
            command: The generate demand letter command
            consolidated_info: The consolidated case information

        Returns:
            A formatted prompt string
        """
        # Check if we have image analysis results
        has_image_analysis = bool(command.image_analysis_result or command.accident_scene_analysis or command.property_damage_analysis)

        # Create specialized section instructions
        specialized_sections = []
        client_name = command.client_name or "the client"

        # 1. Visual Evidence Section (if applicable)
        if has_image_analysis:
            # Prepare detailed visual evidence content based on available analyses
            visual_evidence_details = []

            if command.image_analysis_result:
                visual_evidence_details.append(f"The photographs clearly document {client_name}'s injuries, showing {command.image_analysis_result.split('.')[0]}.")

            if command.accident_scene_analysis:
                visual_evidence_details.append(f"The accident scene photographs demonstrate {command.accident_scene_analysis.split('.')[0]}.")

            if command.property_damage_analysis:
                visual_evidence_details.append(f"The property damage photographs reveal {command.property_damage_analysis.split('.')[0]}.")

            # Join the details into a paragraph
            detailed_evidence = " ".join(visual_evidence_details)

            specialized_sections.append(f"""
            **Visual Evidence Section Requirement:**
            You MUST include a detailed section titled "Visual Evidence" that incorporates the following information:

            "**Visual Evidence**
            Attached herewith are photographs that provide crucial visual documentation of this case. {detailed_evidence} These visual records corroborate the exact nature of {client_name}'s injuries, the accident circumstances, and substantiate their claims with objective evidence that cannot be disputed."

            This section should be placed after the medical treatment section and before the general damages section. Make sure to integrate the specific details from the image analyses into your narrative rather than just mentioning that photos exist.
            """)

        # 2. General Damages Section
        if command.general_damages:
            specialized_sections.append(f"""
            **General Damages Section Requirement:**
            You MUST include a comprehensive section titled "General Damages" that incorporates the detailed analysis previously performed. Do not create a generic damages section - instead, use the specific information provided in the general damages analysis to create a persuasive and detailed section.

            The general damages section should:
            1. Directly reference the specific categories of damages identified in the analysis
            2. Include the specific amounts or ranges suggested for each category
            3. Incorporate the reasoning and justifications provided in the analysis
            4. Maintain the same level of detail and specificity as the original analysis

            This is NOT a place to summarize or simplify - this section should be one of the most detailed and substantive parts of the demand letter, as it forms the core of the monetary demand.
            """)

        # 3. Impact on Lifestyle Section
        if command.impact_on_lifestyle:
            specialized_sections.append(f"""
            **Impact on Lifestyle Section Requirement:**
            You MUST include a detailed section titled "Impact on Lifestyle" that fully incorporates the lifestyle impact analysis previously performed. This section should vividly illustrate how {client_name}'s life has been affected by the injuries.

            The Impact on Lifestyle section should:
            1. Detail specific activities, hobbies, and daily routines that have been impacted
            2. Include emotional and psychological effects mentioned in the analysis
            3. Describe changes to family relationships and social interactions
            4. Emphasize long-term or permanent lifestyle changes
            5. Connect these impacts directly to the compensation being sought

            Use specific examples and details from the lifestyle impact analysis to create a compelling narrative that helps the reader understand the human cost of the injuries beyond just medical expenses.
            """)

        # 4. Medical Treatment Section
        if command.medical_analysis:
            specialized_sections.append(f"""
            **Medical Treatment Section Requirement:**
            You MUST include a detailed section titled "Medical Treatment" that thoroughly incorporates the medical analysis previously performed. This section should provide a comprehensive overview of {client_name}'s medical treatment journey.

            The Medical Treatment section should:
            1. List all healthcare providers and facilities visited in chronological order
            2. Detail specific diagnoses, procedures, and treatments received
            3. Include specific dates of significant medical events when available
            4. Highlight the severity of injuries through medical terminology and expert opinions
            5. Connect the treatments to the accident by establishing clear causation

            Use specific details from the medical analysis to create an authoritative account of the medical aspects of the case.
            """)

        # 5. Future Medical Expenses Section
        if command.future_medical_expenses:
            specialized_sections.append(f"""
            **Future Medical Expenses Section Requirement:**
            You MUST include a detailed section titled "Future Medical Expenses" that thoroughly incorporates the future treatment analysis previously performed. This section should outline the anticipated ongoing and future medical needs of {client_name}.

            The Future Medical Expenses section should:
            1. List all recommended future treatments and procedures
            2. Include specific cost estimates for each treatment when available
            3. Reference medical expert opinions supporting the need for future care
            4. Explain how these treatments relate to the injuries sustained
            5. Provide a total estimated cost for all future medical care

            Use specific details from the future treatment analysis to create a well-supported projection of future medical needs and their associated costs.
            """)

        # 6. Facts and Liability Section
        if command.facts_and_liability:
            specialized_sections.append(f"""
            **Facts and Liability Section Requirement:**
            You MUST include detailed sections titled "Facts" and "Liability" that thoroughly incorporate the facts and liability analysis previously performed. These sections should establish a clear narrative of the incident and a compelling argument for liability.

            The Facts section should:
            1. Provide a chronological account of the incident with specific details
            2. Include date, time, location, and weather conditions if available
            3. Describe the actions of all parties involved
            4. Reference any witness statements or police reports
            5. Present the facts objectively but in a way that supports your client's position

            The Liability section should:
            1. Clearly state who is at fault and why
            2. Reference specific laws, regulations, or legal standards that were violated
            3. Connect the facts to the legal standards to establish negligence
            4. Address and refute any potential comparative negligence arguments
            5. Present a confident and authoritative position on liability

            Use specific details from the facts and liability analysis to create a compelling narrative that establishes clear responsibility for the incident.
            """)

        # Combine all specialized section instructions
        specialized_sections_text = "\n\n".join(specialized_sections)

        # Get tone and emphasis preferences
        tone = command.tone.lower() if command.tone else "firm"
        emphasis = command.emphasis or {
            'medical_damages': 3,
            'quality_of_life': 3,
            'economic_losses': 3,
            'liability': 3
        }

        # Get length preference
        length = command.length.lower() if command.length else "standard"

        # Create length instructions based on selected length
        length_instructions = {
            "basic": "Create a concise demand letter focusing only on the most essential elements. Keep sections brief and to the point, with minimal elaboration.",
            "standard": "Create a balanced demand letter with appropriate detail in each section. Include all relevant information without being overly verbose.",
            "large": "Create a comprehensive demand letter with extensive detail in each section. Elaborate on all aspects of the case, providing thorough explanations and justifications."
        }.get(length, "Create a balanced demand letter with appropriate detail in each section.")

        # Create tone instructions based on selected tone
        tone_instructions = {
            "firm": "Use a firm, professional tone that is assertive without being aggressive. Be direct and clear about expectations while maintaining professionalism.",
            "neutral": "Use a neutral, objective tone that focuses on facts and evidence. Avoid emotional language and maintain a balanced perspective throughout.",
            "conciliatory": "Use a conciliatory tone that emphasizes cooperation and resolution. Acknowledge the other party's perspective while still advocating for your client's interests.",
            "aggressive": "Use a strong, assertive tone that emphasizes the strength of your client's case. Be direct about liability and expectations for compensation."
        }.get(tone, "Use a firm, professional tone that is assertive without being aggressive.")

        # Create emphasis instructions based on slider values
        emphasis_instructions = []

        # Medical damages emphasis
        if emphasis.get('medical_damages', 3) >= 4:
            emphasis_instructions.append("Place strong emphasis on the medical damages and injuries. Dedicate significant space to detailing the medical treatments, diagnoses, and their impacts.")
        elif emphasis.get('medical_damages', 3) <= 2:
            emphasis_instructions.append("Keep the medical damages section concise while covering the essential information.")

        # Quality of life emphasis
        if emphasis.get('quality_of_life', 3) >= 4:
            emphasis_instructions.append("Highlight the impact on lifestyle and quality of life as a central theme throughout the letter. Provide detailed examples of how the client's daily life has been affected.")
        elif emphasis.get('quality_of_life', 3) <= 2:
            emphasis_instructions.append("Address the quality of life impacts briefly but focus more on other aspects of the case.")

        # Economic losses emphasis
        if emphasis.get('economic_losses', 3) >= 4:
            emphasis_instructions.append("Emphasize the economic losses and financial impacts. Provide detailed calculations and justifications for all economic damages claimed.")
        elif emphasis.get('economic_losses', 3) <= 2:
            emphasis_instructions.append("Cover economic losses concisely without extensive detail.")

        # Liability emphasis
        if emphasis.get('liability', 3) >= 4:
            emphasis_instructions.append("Make liability a central focus of the letter. Provide detailed legal arguments establishing clear fault and responsibility.")
        elif emphasis.get('liability', 3) <= 2:
            emphasis_instructions.append("Address liability clearly but concisely without extensive legal arguments.")

        # Combine emphasis instructions
        emphasis_text = "\n".join(f"- {instruction}" for instruction in emphasis_instructions)

        # This is a simplified version of the prompt from case_builder.py
        prompt = f"""
            As an AI legal assistant, draft a comprehensive demand letter for a personal injury case under '{command.liability_type}' in {command.selected_state}. The letter should include all relevant sections such as:

            - Introduction
            - Facts
            - Liability
            - Damages
            - Medical Treatment
            - Future Medical Expenses
            - Impact on Lifestyle
            - General Damages Estimation
            {"- Visual Evidence" if has_image_analysis else ""}
            - Policy Limit Demand
            - Conclusion

            {length_instructions}

            Use the consolidated information provided to create a persuasive and legally sound demand letter. Ensure that all details are accurate and based solely on the information given. Do not fabricate or assume any information not included in the consolidated data.

            {specialized_sections_text}

            **Tone and Style Requirements:**
            {tone_instructions}

            **Content Emphasis:**
            {emphasis_text}

            **Important:**
            - The level of detail and the length of the demand letter must adjust according to the demand amount provided.
                - **If the demand amount is "Policy Limit"** (typically representing a range between 30K-50K): Create a standard, concise demand letter.
                - **If the demand amount is between 100K and 499K:** Prepare a demand letter with enhanced detail, additional legal analysis, and supporting arguments.
                - **If the demand amount is greater than 500K:** Produce an extended demand letter spanning multiple pages, including in-depth sections with comprehensive legal arguments and detailed breakdowns of damages.

            **Guidelines:**
            - **Accuracy**: Reflect the exact details from the consolidated information.
            - **No Fabrication**: Do not introduce any details that are not provided.
            - **Structure**: Follow the outlined sections strictly and adjust the depth of each section based on the demand amount.
            - **Customization by Demand Amount**: Ensure that the overall tone, level of detail, and document length are appropriate for the specified policy limit as indicated above.
        """

        return prompt
