from typing import Dict, Any, Optional

from ..commands.medical_analysis_commands import (
    DetailedMedicalAnalysisCommand,
    MedicalExpensesAnalysisCommand,
    FutureMedicalExpensesCommand
)
from ..commands.command_base import Command
from .command_handler import CommandHandler
from ...core.interfaces.IAIService import IAIService
from ...core.interfaces.ITextProcessorService import ITextProcessorService

class DetailedMedicalAnalysisHandler(CommandHandler[str]):
    """
    Handler for generating detailed medical analysis from provided documents.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: DetailedMedicalAnalysisCommand) -> str:
        """
        Process the provided document text to generate a detailed medical analysis.

        Args:
            command: The medical analysis command with document text and optional parameters

        Returns:
            A comprehensive medical analysis as a formatted string
        """
        # Generate the prompt for analysis
        prompt = self._create_analysis_prompt(command)

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt-4o-mini",  # Changed from gpt4omini
            text=command.document_text,
            prompt=prompt
        )

        return result

    def _create_analysis_prompt(self, command: DetailedMedicalAnalysisCommand) -> str:
        """
        Create the prompt for medical analysis based on the command.

        Args:
            command: The medical analysis command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''
        style = command.style.lower() if command.style else "narrative"
        detail_level = command.detail_level.lower() if command.detail_level else "moderate"
        include_icd = getattr(command, 'include_icd', False)

        # Create style instructions based on selected style
        style_instructions = {
            "narrative": "Present the medical analysis in a flowing narrative format that tells the story of the client's medical journey. Use paragraphs to organize information and create a cohesive story. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers or section titles - integrate all information into a continuous narrative.",
            "chronological": "Present the medical analysis in strict chronological order, with clear date markers for each medical event. Organize information by date to show the progression of treatment over time.",
            "bulleted": "Present the medical analysis using bullet points for clarity and easy reference. Organize information into categories with bullet points for each significant finding or treatment.",
            "tabular": "Present the medical analysis using tables where appropriate, especially for treatments, medications, and medical expenses. Use a structured format for easy comparison of information."
        }.get(style, "Present the medical analysis in a flowing narrative format.")

        # Create detail level instructions
        detail_instructions = {
            "brief": "Provide a concise summary of the most important medical information. Focus only on key injuries, major treatments, and significant findings. Omit minor details and routine visits.",
            "moderate": "Provide a balanced analysis with essential details. Include all significant medical events and treatments while summarizing routine care. Focus on information relevant to the case.",
            "comprehensive": "Provide an exhaustive analysis with all available medical details. Include every treatment, visit, medication, and finding from the records. Be thorough and complete in your analysis."
        }.get(detail_level, "Provide a balanced analysis with essential details.")

        # Create ICD code instructions
        icd_instruction = ""
        if include_icd:
            # Always include a table for ICD codes regardless of style
            icd_instruction = """
            IMPORTANT: Regardless of the selected style, always include a separate table for ICD codes with the following columns:
            - Treatment/Injury
            - ICD-10 Code
            - Page Reference

            This table should be clearly labeled as "ICD DIAGNOSIS CODES TABLE" and include all relevant diagnostic codes found in the medical records.
            Place this table at the end of your analysis.

            Even if you're writing in narrative style, still include this separate table at the end.
            """

        # Adjust the prompt based on the style
        if style == "narrative":
            prompt = f"""
                As an AI medical analyst, your task is to provide an analysis of the medical records for {client_name}.

                {style_instructions}

                {detail_instructions}

                Your narrative should cover the injuries sustained, how they affected the client's life, the timeline of medical care (organized by provider if multiple but preserving the sequence of events), and integrate any user-provided notes to fill in gaps or clarify events.

                {icd_instruction}

                **Additional Notes (if applicable):**
                {additional_notes}
            """
        else:
            prompt = f"""
                As an AI medical analyst, your task is to provide an analysis of the medical records for {client_name}.

                {style_instructions}

                {detail_instructions}

                Your analysis should include:
                1. A review of the injuries sustained and how they affected the client's life.
                2. A timeline of medical care, organized by provider (if multiple) but preserving the sequence of events.
                3. Integration of user-provided notes (if available) to fill in gaps or clarify events.

                {icd_instruction}

                **Additional Notes (if applicable):**
                {additional_notes}
            """

        return prompt


class MedicalExpensesAnalysisHandler(CommandHandler[str]):
    """
    Handler for identifying and compiling medical expenses from provided documents.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: MedicalExpensesAnalysisCommand) -> str:
        """
        Process the provided document text to extract and compile medical expenses.

        Args:
            command: The medical expenses analysis command with document text and optional parameters

        Returns:
            A medical expenses analysis as a formatted string with table
        """
        # Generate the prompt for analysis
        prompt = self._create_expenses_prompt(command)

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt-4o-mini",  # Changed from gpt4omini
            text=command.document_text,
            prompt=prompt
        )

        return result

    def _create_expenses_prompt(self, command: MedicalExpensesAnalysisCommand) -> str:
        """
        Create the prompt for medical expenses analysis based on the command.

        Args:
            command: The medical expenses analysis command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''

        # Determine style instructions
        style = command.style.lower() if hasattr(command, 'style') else 'tabular'
        detail_level = command.detail_level.lower() if hasattr(command, 'detail_level') else 'moderate'

        style_instructions = {
            'narrative': 'Provide the medical expenses analysis in a detailed narrative format, integrating costs and provider information into the text. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers, section titles, or any structured formatting - integrate all information into a continuous narrative that flows naturally.',
            'chronological': 'Organize the medical expenses analysis in chronological order, listing each expense event by date with associated details.',
            'bulleted': 'Present the medical expenses analysis as bulleted points for clarity, grouping by provider or category as needed.',
            'tabular': 'Present the medical expenses in a clear table format.'
        }.get(style, 'Present the medical expenses in a clear table format.')

        # Create detail level instructions
        detail_instructions = {
            "brief": "Provide a concise summary of only the most significant medical expenses. Group smaller expenses together and focus on major costs.",
            "moderate": "Provide a balanced analysis of medical expenses with moderate detail. Include all significant costs while summarizing minor expenses.",
            "comprehensive": "Provide an exhaustive breakdown of all medical expenses with complete detail. Include every charge, no matter how small."
        }.get(detail_level, "Provide a balanced analysis of medical expenses with moderate detail.")

        # ICD codes inclusion
        include_icd = getattr(command, 'include_icd', False)
        icd_instruction = ''

        if include_icd:
            # Always include a table for ICD codes regardless of style
            icd_instruction = """
            IMPORTANT: Regardless of the selected style, always include a separate table for ICD codes with the following columns:
            - Treatment/Injury
            - ICD-10 Code
            - Page Reference

            This table should be clearly labeled as "ICD DIAGNOSIS CODES TABLE" and include all relevant diagnostic codes found in the medical records.
            Place this table at the end of your analysis.

            Even if you're writing in narrative style, still include this separate table at the end.
            """

        # Adjust the prompt based on the style
        if style == "narrative":
            prompt = f"""
                As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.
                {style_instructions}

                {detail_instructions}

                In your narrative, be sure to include information about each medical provider/facility, their treatment/procedure summary, treatment period, total cost, and page references where appropriate. Weave this information naturally into your narrative without using lists or bullet points.

                {icd_instruction}
            """
        else:
            prompt = f"""
                As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.
                {style_instructions}

                {detail_instructions}

                Specifically, identify and for each medical provider/facility:
                - Provider/Facility Name
                - Treatment/Procedure Summary (summarized description)
                - Treatment Period (From – To)
                - Total Cost
                - Include page references [Page X] where appropriate

                {icd_instruction}
            """
        return prompt


class FutureMedicalExpensesHandler(CommandHandler[str]):
    """
    Handler for projecting future medical expenses based on the medical records.
    """

    def __init__(self, ai_service: IAIService, text_processor: ITextProcessorService):
        self.ai_service = ai_service
        self.text_processor = text_processor

    async def handle(self, command: FutureMedicalExpensesCommand) -> str:
        """
        Process the provided document text to project future medical expenses.

        Args:
            command: The future medical expenses command with document text and optional parameters

        Returns:
            A future medical expenses projection as a formatted string with table
        """
        # Generate the prompt for analysis
        prompt = self._create_future_expenses_prompt(command)

        # Use the existing medical analysis if provided, otherwise use the document text
        input_text = command.existing_medical_analysis or command.document_text

        # Process text through text processor to handle large documents
        result = await self.text_processor.process_text_in_segments(
            model_alias="gpt-4o-mini",  # Changed from gpt4omini
            text=input_text,
            prompt=prompt
        )

        return result

    def _create_future_expenses_prompt(self, command: FutureMedicalExpensesCommand) -> str:
        """
        Create the prompt for future medical expenses projection based on the command.

        Args:
            command: The future medical expenses command

        Returns:
            A formatted prompt string
        """
        client_name = command.client_name or 'the client'
        additional_notes = command.additional_notes or ''

        # Determine style instructions
        style = command.style.lower() if hasattr(command, 'style') else 'tabular'
        detail_level = command.detail_level.lower() if hasattr(command, 'detail_level') else 'moderate'

        style_instructions = {
            'narrative': 'Provide a detailed narrative of the anticipated future medical expenses, explaining their purpose and importance. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers, section titles, or any structured formatting - integrate all information into a continuous narrative that flows naturally.',
            'chronological': 'Organize the future medical expenses chronologically by anticipated date, detailing each expense event.',
            'bulleted': 'Present the future medical expenses as bulleted items for clarity, grouping by type or provider.',
            'tabular': 'Present the future medical expenses in a well-structured table format.'
        }.get(style, 'Present the future medical expenses in a well-structured table format.')

        # Create detail level instructions
        detail_instructions = {
            "brief": "Focus only on the most significant future medical needs. Provide a concise projection of major anticipated expenses.",
            "moderate": "Provide a balanced projection of future medical needs with reasonable detail. Include all significant anticipated treatments.",
            "comprehensive": "Provide an exhaustive projection of all possible future medical needs. Include every potential treatment, no matter how minor."
        }.get(detail_level, "Provide a balanced projection of future medical needs with reasonable detail.")

        # ICD codes inclusion
        include_icd = getattr(command, 'include_icd', False)
        icd_instruction = ''

        if include_icd:
            # Always include a table for ICD codes regardless of style
            icd_instruction = """
            IMPORTANT: Regardless of the selected style, always include a separate table for projected ICD codes with the following columns:
            - Treatment/Injury
            - ICD-10 Code
            - Page Reference (if applicable from source records)

            This table should be clearly labeled as "PROJECTED ICD DIAGNOSIS CODES TABLE" and include all relevant diagnostic codes that are projected for future treatments.
            Place this table at the end of your analysis.

            Even if you're writing in narrative style, still include this separate table at the end.
            """

        # Adjust the prompt based on the style
        if style == "narrative":
            prompt = f"""
                As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.
                {style_instructions}

                {detail_instructions}

                In your narrative, be sure to include information about each future medical expense, including frequency and duration, cost per session, and total estimated cost. Weave this information naturally into your narrative without using lists or bullet points.

                {icd_instruction}
            """
        else:
            prompt = f"""
                As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.
                {style_instructions}

                {detail_instructions}

                Your analysis should identify:
                - Future Medical Expense
                - Frequency and Duration
                - Cost per Session
                - Total Estimated Cost
                - For non-tabular formats, integrate these details into the text as appropriate.

                {icd_instruction}
            """
        return prompt
